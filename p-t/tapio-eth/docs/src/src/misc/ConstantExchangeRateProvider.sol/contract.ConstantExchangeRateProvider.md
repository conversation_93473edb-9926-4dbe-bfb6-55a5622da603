# ConstantExchangeRateProvider

**Inherits:** [IExchangeRateProvider](/src/interfaces/IExchangeRateProvider.sol/interface.IExchangeRateProvider.md)

Constant exchange rate provider.

## Functions

### exchangeRate

_Get the exchange rate_

```solidity
function exchangeRate() external pure returns (uint256);
```

### exchangeRateDecimals

_Get the exchange rate decimals_

```solidity
function exchangeRateDecimals() external pure returns (uint256);
```
