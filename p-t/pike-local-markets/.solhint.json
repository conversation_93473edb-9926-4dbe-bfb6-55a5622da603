{"extends": "solhint:recommended", "rules": {"compiler-version": ["warn", "0.8.28"], "func-visibility": ["warn", {"ignoreConstructors": true}], "reentrancy": "error", "state-visibility": "error", "quotes": ["error", "double"], "const-name-snakecase": "error", "contract-name-camelcase": "error", "event-name-camelcase": "error", "func-name-mixedcase": "error", "func-param-name-mixedcase": "error", "modifier-name-mixedcase": "error", "private-vars-leading-underscore": ["off", {"strict": true}], "use-forbidden-name": "error", "var-name-mixedcase": "error", "imports-on-top": "error", "ordering": "error", "visibility-modifier-order": "error", "code-complexity": ["error", 15], "function-max-lines": ["error", 90], "max-line-length": ["warn", 120], "max-states-count": ["error", 15], "no-empty-blocks": "warn", "no-unused-vars": "error", "payable-fallback": "off", "constructor-syntax": "error", "explicit-types": "error", "no-unused-import": "warn", "one-contract-per-file": "warn", "not-rely-on-time": "off", "reason-string": "error", "no-inline-assembly": "off", "avoid-low-level-calls": "off", "no-complex-fallback": "off", "no-global-import": "off", "avoid-tx-origin": "off", "gas-custom-errors": "off"}, "plugins": ["prettier"]}