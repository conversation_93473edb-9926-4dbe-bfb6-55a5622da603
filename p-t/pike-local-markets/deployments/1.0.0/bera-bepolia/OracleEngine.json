{"address": "0x38a3412bebF667f4853A439367c5D99C2Dd01f53", "abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "configs", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IOracleEngine.AssetConfig", "components": [{"name": "mainOracle", "type": "address", "internalType": "address"}, {"name": "fallback<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "lowerBoundRatio", "type": "uint256", "internalType": "uint256"}, {"name": "upperBoundRatio", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPrice", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "price", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getUnderlyingPrice", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "configurator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAssetConfig", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "mainOracle", "type": "address", "internalType": "address"}, {"name": "fallback<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "lowerBoundRatio", "type": "uint256", "internalType": "uint256"}, {"name": "upperBoundRatio", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "AssetConfigSet", "inputs": [{"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "mainOracle", "type": "address", "indexed": false, "internalType": "address"}, {"name": "fallback<PERSON><PERSON><PERSON>", "type": "address", "indexed": false, "internalType": "address"}, {"name": "lowerBoundRatio", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "upperBoundRatio", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "BoundValidationFailed", "inputs": []}, {"type": "error", "name": "InvalidAsset", "inputs": []}, {"type": "error", "name": "InvalidBounds", "inputs": []}, {"type": "error", "name": "InvalidFallbackOraclePrice", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidMainOracle", "inputs": []}, {"type": "error", "name": "InvalidMainOraclePrice", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}], "constructorArgs": [], "linkedLibraries": {}, "deployTxnHash": "0xc3ddf27e0dda2e28a8002dabec496e1ce411744a7262688ba0de6bbf18bbe77f", "deployTxnBlockNumber": "2082834", "deployTimestamp": "**********", "sourceName": "src/oracles/OracleEngine.sol", "contractName": "src/oracles/OracleEngine.sol:OracleEngine", "deployedOn": "contract.OracleEngine", "gasUsed": 1056504, "gasCost": "5000000007"}