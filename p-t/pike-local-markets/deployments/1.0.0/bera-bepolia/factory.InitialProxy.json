{"address": "0x4790A15ca53622dA5352bf0d19C73e3f51431627", "abi": [{"type": "constructor", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}], "constructorArgs": ["0xD1593d39df59e4Cc0210E74E0aBb1D42483B4b3D", "0x"], "linkedLibraries": {}, "deployTxnHash": "0x5903ef1a066a2473c2457651d83098ee0c46f73908fe4530b95411ae4ea60f04", "deployTxnBlockNumber": "1092900", "deployTimestamp": "1742243596", "sourceName": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "contractName": "ERC1967Proxy", "deployedOn": "deploy.InitialProxy", "gasUsed": 132270, "gasCost": "5000000007"}