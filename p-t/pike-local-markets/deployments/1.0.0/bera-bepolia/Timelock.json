{"address": "0x0200637680D76c323c83B9f3f42dDE1ceB468193", "abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "CANCELLER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "EMERGENCY_GUARDIAN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "EXECUTOR_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "PROPOSER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "cancel", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "emergencyExecute", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "payload", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "emergencyExecuteBatch", "inputs": [{"name": "targets", "type": "address[]", "internalType": "address[]"}, {"name": "values", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "payloads", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "execute", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "payload", "type": "bytes", "internalType": "bytes"}, {"name": "predecessor", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "executeBatch", "inputs": [{"name": "targets", "type": "address[]", "internalType": "address[]"}, {"name": "values", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "payloads", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "predecessor", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "get<PERSON>in<PERSON>elay", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOperationState", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum TimelockControllerUpgradeable.OperationState"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getTimestamp", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hashOperation", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "predecessor", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "hashOperationBatch", "inputs": [{"name": "targets", "type": "address[]", "internalType": "address[]"}, {"name": "values", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "payloads", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "predecessor", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "initialize", "inputs": [{"name": "admin", "type": "address", "internalType": "address"}, {"name": "min<PERSON>elay", "type": "uint256", "internalType": "uint256"}, {"name": "proposers", "type": "address[]", "internalType": "address[]"}, {"name": "executors", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "min<PERSON>elay", "type": "uint256", "internalType": "uint256"}, {"name": "proposers", "type": "address[]", "internalType": "address[]"}, {"name": "executors", "type": "address[]", "internalType": "address[]"}, {"name": "admin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isOperation", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperationDone", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperationPending", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperationReady", "inputs": [{"name": "id", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "onERC1155BatchReceived", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC1155Received", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC721Received", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "schedule", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "predecessor", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "delay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "scheduleBatch", "inputs": [{"name": "targets", "type": "address[]", "internalType": "address[]"}, {"name": "values", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "payloads", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "predecessor", "type": "bytes32", "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "delay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "updateDelay", "inputs": [{"name": "newDelay", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "CallExecuted", "inputs": [{"name": "id", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "index", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "target", "type": "address", "indexed": false, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "data", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "CallSalt", "inputs": [{"name": "id", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "salt", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "CallScheduled", "inputs": [{"name": "id", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "index", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "target", "type": "address", "indexed": false, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "data", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "predecessor", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "delay", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Cancelled", "inputs": [{"name": "id", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "MinDelayChange", "inputs": [{"name": "oldDuration", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newDuration", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "FailedCall", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "TimelockInsufficientDelay", "inputs": [{"name": "delay", "type": "uint256", "internalType": "uint256"}, {"name": "min<PERSON>elay", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TimelockInvalidOperationLength", "inputs": [{"name": "targets", "type": "uint256", "internalType": "uint256"}, {"name": "payloads", "type": "uint256", "internalType": "uint256"}, {"name": "values", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TimelockUnauthorizedCaller", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TimelockUnexecutedPredecessor", "inputs": [{"name": "predecessorId", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "TimelockUnexpectedOperationState", "inputs": [{"name": "operationId", "type": "bytes32", "internalType": "bytes32"}, {"name": "expectedStates", "type": "bytes32", "internalType": "bytes32"}]}], "constructorArgs": [], "linkedLibraries": {}, "deployTxnHash": "0x8917f779556a7d359688d1ba66ce31719152c52f6ed4b0e6d23cc546b1d05a79", "deployTxnBlockNumber": "2082847", "deployTimestamp": "1744042026", "sourceName": "src/governance/Timelock.sol", "contractName": "src/governance/Timelock.sol:Timelock", "deployedOn": "contract.Timelock", "gasUsed": 2443800, "gasCost": "5000000007"}