{"address": "0x80ea2ec3CeCDcC964ECCE46693EDB9C8848890bC", "abi": [{"type": "function", "name": "accountCategory", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "borrowAllowed", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowCap", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "checkBorrowMembership", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "checkCollateralMembership", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "closeFactor", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "collateralFactor", "inputs": [{"name": "categoryId", "type": "uint8", "internalType": "uint8"}, {"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "configureEMode", "inputs": [{"name": "categoryId", "type": "uint8", "internalType": "uint8"}, {"name": "baseConfig", "type": "tuple", "internalType": "struct IRiskEngine.BaseConfiguration", "components": [{"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationThresholdMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "configureMarket", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}, {"name": "baseConfig", "type": "tuple", "internalType": "struct IRiskEngine.BaseConfiguration", "components": [{"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationThresholdMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegateAllowed", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "delegate", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "emodeMarkets", "inputs": [{"name": "categoryId", "type": "uint8", "internalType": "uint8"}], "outputs": [{"name": "collateralTokens", "type": "address[]", "internalType": "address[]"}, {"name": "borrowTokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "enterMarkets", "inputs": [{"name": "pTokens", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitMarket", "inputs": [{"name": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAccountBorrowLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getAllMarkets", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "contract IPToken[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAssetsIn", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "contract IPToken[]"}], "stateMutability": "view"}, {"type": "function", "name": "getHypotheticalAccountLiquidity", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "pTokenModify", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getReserveShares", "inputs": [], "outputs": [{"name": "ownerShareMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "configuratorShareMantissa", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isDeprecated", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateBorrowAllowed", "inputs": [{"name": "pT<PERSON>Borrowed", "type": "address", "internalType": "address"}, {"name": "pTokenCollateral", "type": "address", "internalType": "address"}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "repayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "view"}, {"type": "function", "name": "liquidateCalculateSeizeTokens", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "pT<PERSON>Borrowed", "type": "address", "internalType": "address"}, {"name": "pTokenCollateral", "type": "address", "internalType": "address"}, {"name": "actualRepayAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidationIncentive", "inputs": [{"name": "categoryId", "type": "uint8", "internalType": "uint8"}, {"name": "pToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "liquidationThreshold", "inputs": [{"name": "categoryId", "type": "uint8", "internalType": "uint8"}, {"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mintAllowed", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "pToken", "type": "address", "internalType": "address"}, {"name": "mintAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "view"}, {"type": "function", "name": "mintVerify", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "oracle", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "redeemAllowed", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}, {"name": "redeemer", "type": "address", "internalType": "address"}, {"name": "redeemTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "view"}, {"type": "function", "name": "repayBorrowAllowed", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "view"}, {"type": "function", "name": "repayBorrowVerify", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "seizeAllowed", "inputs": [{"name": "pTokenCollateral", "type": "address", "internalType": "address"}, {"name": "pT<PERSON>Borrowed", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "view"}, {"type": "function", "name": "setBorrowPaused", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setCloseFactor", "inputs": [{"name": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "newCloseFactorMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketBorrowCaps", "inputs": [{"name": "pTokens", "type": "address[]", "internalType": "contract IPToken[]"}, {"name": "newBorrowCaps", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMarketSupplyCaps", "inputs": [{"name": "pTokens", "type": "address[]", "internalType": "contract IPToken[]"}, {"name": "newSupplyCaps", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMintPaused", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}, {"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "newOracle", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReserveShares", "inputs": [{"name": "newOwnerShareMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "newConfiguratorShareMantissa", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setSeizePaused", "inputs": [{"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTransferPaused", "inputs": [{"name": "state", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyCap", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "supportEMode", "inputs": [{"name": "categoryId", "type": "uint8", "internalType": "uint8"}, {"name": "isAllowed", "type": "bool", "internalType": "bool"}, {"name": "pTokens", "type": "address[]", "internalType": "address[]"}, {"name": "collateralPermissions", "type": "bool[]", "internalType": "bool[]"}, {"name": "borrowPermissions", "type": "bool[]", "internalType": "bool[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportMarket", "inputs": [{"name": "pToken", "type": "address", "internalType": "contract IPToken"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "switchEMode", "inputs": [{"name": "newCategoryId", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferAllowed", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}, {"name": "src", "type": "address", "internalType": "address"}, {"name": "transferTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint8", "internalType": "enum RiskEngineError.Error"}], "stateMutability": "view"}, {"type": "function", "name": "updateDelegate", "inputs": [{"name": "delegate", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "ActionPaused", "inputs": [{"name": "action", "type": "string", "indexed": false, "internalType": "string"}, {"name": "pauseState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "ActionPaused", "inputs": [{"name": "pToken", "type": "address", "indexed": true, "internalType": "contract IPToken"}, {"name": "action", "type": "string", "indexed": false, "internalType": "string"}, {"name": "pauseState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "DelegateUpdated", "inputs": [{"name": "approver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "delegate", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "EModeSwitched", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "oldCategory", "type": "uint8", "indexed": false, "internalType": "uint8"}, {"name": "newCategory", "type": "uint8", "indexed": false, "internalType": "uint8"}], "anonymous": false}, {"type": "event", "name": "EModeUpdated", "inputs": [{"name": "categoryId", "type": "uint8", "indexed": false, "internalType": "uint8"}, {"name": "pToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "allowed", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "collateralStatus", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "borrowStatus", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "MarketEntered", "inputs": [{"name": "pToken", "type": "address", "indexed": false, "internalType": "contract IPToken"}, {"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketExited", "inputs": [{"name": "pToken", "type": "address", "indexed": false, "internalType": "contract IPToken"}, {"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "MarketListed", "inputs": [{"name": "pToken", "type": "address", "indexed": false, "internalType": "contract IPToken"}], "anonymous": false}, {"type": "event", "name": "NewBorrowCap", "inputs": [{"name": "pToken", "type": "address", "indexed": true, "internalType": "contract IPToken"}, {"name": "newBorrowCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewCloseFactor", "inputs": [{"name": "pToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "oldCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newCloseFactorMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewEModeConfiguration", "inputs": [{"name": "categoryId", "type": "uint8", "indexed": false, "internalType": "uint8"}, {"name": "oldConfig", "type": "tuple", "indexed": false, "internalType": "struct IRiskEngine.BaseConfiguration", "components": [{"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationThresholdMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}]}, {"name": "newConfig", "type": "tuple", "indexed": false, "internalType": "struct IRiskEngine.BaseConfiguration", "components": [{"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationThresholdMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}]}], "anonymous": false}, {"type": "event", "name": "NewMarketConfiguration", "inputs": [{"name": "pToken", "type": "address", "indexed": false, "internalType": "contract IPToken"}, {"name": "oldConfig", "type": "tuple", "indexed": false, "internalType": "struct IRiskEngine.BaseConfiguration", "components": [{"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationThresholdMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}]}, {"name": "newConfig", "type": "tuple", "indexed": false, "internalType": "struct IRiskEngine.BaseConfiguration", "components": [{"name": "collateralFactorMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationThresholdMantissa", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationIncentiveMantissa", "type": "uint256", "internalType": "uint256"}]}], "anonymous": false}, {"type": "event", "name": "NewOracleEngine", "inputs": [{"name": "oldOracleEngine", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newOracleEngine", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewReserveShares", "inputs": [{"name": "newOwnerShareMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newConfiguratorShareMantissa", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "NewSupplyCap", "inputs": [{"name": "pToken", "type": "address", "indexed": true, "internalType": "contract IPToken"}, {"name": "newSupplyCap", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AlreadyInEMode", "inputs": []}, {"type": "error", "name": "AlreadyListed", "inputs": []}, {"type": "error", "name": "BorrowPaused", "inputs": []}, {"type": "error", "name": "DelegationStatusUnchanged", "inputs": []}, {"type": "error", "name": "ExitMarketRedeemRejection", "inputs": [{"name": "errorCode", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidBorrowStatus", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidCategory", "inputs": []}, {"type": "error", "name": "InvalidCloseFactor", "inputs": []}, {"type": "error", "name": "InvalidCollateralFactor", "inputs": []}, {"type": "error", "name": "InvalidCollateralStatus", "inputs": [{"name": "pToken", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidIncentiveThreshold", "inputs": []}, {"type": "error", "name": "InvalidLiquidationThreshold", "inputs": []}, {"type": "error", "name": "InvalidPermission", "inputs": []}, {"type": "error", "name": "InvalidReserveShare", "inputs": []}, {"type": "error", "name": "MarketNotListed", "inputs": []}, {"type": "error", "name": "MintPaused", "inputs": []}, {"type": "error", "name": "NestedPermissionDenied", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NoArrayParity", "inputs": []}, {"type": "error", "name": "NotListed", "inputs": []}, {"type": "error", "name": "PermissionDenied", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}, {"name": "", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "RepayMoreThanBorrowed", "inputs": []}, {"type": "error", "name": "SeizePaused", "inputs": []}, {"type": "error", "name": "SenderNotPToken", "inputs": []}, {"type": "error", "name": "SwitchEMode", "inputs": [{"name": "errorCode", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TransferPaused", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "constructorArgs": [], "linkedLibraries": {}, "deployTxnHash": "0x8d098b8997c9a31d3559984a897c845800bce74d4e6d5ace53014c2834f2715d", "deployTxnBlockNumber": "11139830", "deployTimestamp": "1744041954", "sourceName": "src/pike-market/modules/riskEngine/RiskEngineModule.sol", "contractName": "src/pike-market/modules/riskEngine/RiskEngineModule.sol:RiskEngineModule", "deployedOn": "contract.RiskEngineModule", "gasUsed": 5248810, "gasCost": "52000000000"}