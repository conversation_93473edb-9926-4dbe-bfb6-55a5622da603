{"address": "0x681566B53c108B5E62460d19932B6365E8b1fC5a", "abi": [{"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded2", "inputs": [{"name": "self", "type": "address", "indexed": true, "internalType": "address"}, {"name": "implementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}], "constructorArgs": [], "linkedLibraries": {}, "deployTxnHash": "0x5bc7f4b1036784371a381a20cf3bf070686ddf1a704b500b02d5c7cf1066aaaa", "deployTxnBlockNumber": "140236241", "deployTimestamp": "1744053468", "sourceName": "test/mocks/UUPSUpgradeableMock.sol", "contractName": "UUPSUpgradeableMock", "deployedOn": "deploy.UUPSUpgradeableMock", "gasUsed": 539961, "gasCost": "100000000"}