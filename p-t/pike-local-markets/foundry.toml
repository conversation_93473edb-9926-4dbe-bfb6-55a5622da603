[profile.default]
solc = '0.8.28'
evm_version = "cancun"
src = "src"
out = "out"
libs = ["lib"]
fs_permissions = [{ access = "read-write", path = "./"}]
optimizer = true
optimizer_runs = 10000
remappings = [
  "@openzeppelin/contracts-upgradeable/=node_modules/@openzeppelin/contracts-upgradeable/",
  "@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/",
  "@chainlink/contracts/=node_modules/@chainlink/contracts/src/v0.8/",
  "@pythnetwork/=node_modules/@pythnetwork/pyth-sdk-solidity/",
  "forge-std/=node_modules/forge-std/src/",
  "cannon-std/=node_modules/cannon-std/src/",
  "@helpers/=test/helpers/",
  "@mocks/=test/mocks/",
  "@interfaces/=src/pike-market/interfaces/",
  "@storage/=src/pike-market/storage/",
  "@modules/=src/pike-market/modules/",
  "@errors/=src/pike-market/errors/",
  "@utils/=src/pike-market/utils/",
  "@oracles/=src/oracles/",
  "@governance/=src/governance/",
  "@factory/=src/"
]
ffi = true

[fuzz]
runs = 10000

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
[fmt]
line_length = 90
tab_width = 4
bracket_spacing = false
int_types = 'long'
quote_style = 'double'
number_underscore = 'thousands'
multiline_func_header = 'attributes_first'