<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="7000pt" height="3278pt"
 viewBox="0.00 0.00 6999.55 3277.80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 3273.8)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-3273.8 6995.55,-3273.8 6995.55,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="717.16,-2834.7 717.16,-2893.1 1004.55,-2893.1 1004.55,-2834.7 717.16,-2834.7"/>
<text text-anchor="middle" x="860.85" y="-2876.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="860.85" y="-2859.7" font-family="Times,serif" font-size="14.00">IERC20Errors</text>
<text text-anchor="middle" x="860.85" y="-2842.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="5138.16,-88.5 5138.16,-146.9 5425.55,-146.9 5425.55,-88.5 5138.16,-88.5"/>
<text text-anchor="middle" x="5281.85" y="-130.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5281.85" y="-113.5" font-family="Times,serif" font-size="14.00">IERC721Errors</text>
<text text-anchor="middle" x="5281.85" y="-96.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="5443.16,-88.5 5443.16,-146.9 5730.55,-146.9 5730.55,-88.5 5443.16,-88.5"/>
<text text-anchor="middle" x="5586.85" y="-130.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5586.85" y="-113.5" font-family="Times,serif" font-size="14.00">IERC1155Errors</text>
<text text-anchor="middle" x="5586.85" y="-96.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="1582.17,-3034.9 1582.17,-3269.3 2011.53,-3269.3 2011.53,-3034.9 1582.17,-3034.9"/>
<text text-anchor="middle" x="1796.85" y="-3252.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1796.85" y="-3235.9" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="1796.85" y="-3219.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1582.17,-3210.9 2011.53,-3210.9 "/>
<text text-anchor="start" x="1590.17" y="-3194.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1590.17" y="-3177.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="1590.17" y="-3160.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="1590.17" y="-3143.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="1590.17" y="-3127.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="1590.17" y="-3110.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="1590.17" y="-3093.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
<text text-anchor="start" x="1590.17" y="-3076.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1590.17" y="-3059.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1590.17" y="-3043.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="5748.91,-50.9 5748.91,-184.5 6380.8,-184.5 6380.8,-50.9 5748.91,-50.9"/>
<text text-anchor="middle" x="6064.85" y="-167.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="6064.85" y="-151.1" font-family="Times,serif" font-size="14.00">IERC20Permit</text>
<text text-anchor="middle" x="6064.85" y="-134.3" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="5748.91,-126.1 6380.8,-126.1 "/>
<text text-anchor="start" x="5756.91" y="-109.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="5756.91" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;permit(owner: address, spender: address, value: uint256, deadline: uint256, v: uint8, r: bytes32, s: bytes32)</text>
<text text-anchor="start" x="5756.91" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nonces(owner: address): uint256</text>
<text text-anchor="start" x="5756.91" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;DOMAIN_SEPARATOR(): bytes32</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="1098.42,-2746.7 1098.42,-2981.1 1597.29,-2981.1 1597.29,-2746.7 1098.42,-2746.7"/>
<text text-anchor="middle" x="1347.85" y="-2964.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1347.85" y="-2947.7" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="1347.85" y="-2930.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1098.42,-2922.7 1597.29,-2922.7 "/>
<text text-anchor="start" x="1106.42" y="-2906.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1106.42" y="-2889.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revert(returndata: bytes)</text>
<text text-anchor="start" x="1106.42" y="-2872.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1106.42" y="-2855.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="1106.42" y="-2838.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1106.42" y="-2822.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="1106.42" y="-2805.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1106.42" y="-2788.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionDelegateCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1106.42" y="-2771.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResultFromTarget(target: address, success: bool, returndata: bytes): bytes</text>
<text text-anchor="start" x="1106.42" y="-2754.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes): bytes</text>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="412.16,-2797.1 412.16,-2930.7 699.55,-2930.7 699.55,-2797.1 412.16,-2797.1"/>
<text text-anchor="middle" x="555.85" y="-2914.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="555.85" y="-2897.3" font-family="Times,serif" font-size="14.00">Context</text>
<text text-anchor="middle" x="555.85" y="-2880.5" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="412.16,-2872.3 699.55,-2872.3 "/>
<text text-anchor="start" x="420.16" y="-2855.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="420.16" y="-2838.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="420.16" y="-2822.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="420.16" y="-2805.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-1912.5 0,-2331.7 501.71,-2331.7 501.71,-1912.5 0,-1912.5"/>
<text text-anchor="middle" x="250.85" y="-2315.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="250.85" y="-2298.3" font-family="Times,serif" font-size="14.00">Math</text>
<text text-anchor="middle" x="250.85" y="-2281.5" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-2273.3 501.71,-2273.3 "/>
<text text-anchor="start" x="8" y="-2256.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="8" y="-2239.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryAdd(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="8" y="-2223.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;trySub(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="8" y="-2206.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMul(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="8" y="-2189.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryDiv(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="8" y="-2172.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMod(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="8" y="-2155.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;max(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="8" y="-2139.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;min(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="8" y="-2122.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;average(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="8" y="-2105.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;ceilDiv(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="8" y="-2088.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256): (result: uint256)</text>
<text text-anchor="start" x="8" y="-2071.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="8" y="-2055.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256): uint256</text>
<text text-anchor="start" x="8" y="-2038.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="8" y="-2021.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256): uint256</text>
<text text-anchor="start" x="8" y="-2004.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="8" y="-1987.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256): uint256</text>
<text text-anchor="start" x="8" y="-1971.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="8" y="-1954.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256): uint256</text>
<text text-anchor="start" x="8" y="-1937.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="8" y="-1920.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;unsignedRoundsUp(rounding: Rounding): bool</text>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="107.16,-2797.1 107.16,-2930.7 394.55,-2930.7 394.55,-2797.1 107.16,-2797.1"/>
<text text-anchor="middle" x="250.85" y="-2914.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="250.85" y="-2897.3" font-family="Times,serif" font-size="14.00">Rounding</text>
<text text-anchor="middle" x="250.85" y="-2880.5" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="107.16,-2872.3 394.55,-2872.3 "/>
<text text-anchor="start" x="115.16" y="-2855.7" font-family="Times,serif" font-size="14.00">Floor: 0</text>
<text text-anchor="start" x="115.16" y="-2838.9" font-family="Times,serif" font-size="14.00">Ceil: 1</text>
<text text-anchor="start" x="115.16" y="-2822.1" font-family="Times,serif" font-size="14.00">Trunc: 2</text>
<text text-anchor="start" x="115.16" y="-2805.3" font-family="Times,serif" font-size="14.00">Expand: 3</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge2" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M244.71,-2332.13C243.49,-2483.89 244.11,-2680.51 246.55,-2786.73"/>
<polygon fill="black" stroke="black" points="243.06,-2787.12 246.8,-2797.03 250.06,-2786.95 243.06,-2787.12"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge1" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" d="M254.91,-2797.03C257.49,-2696.28 258.22,-2499.27 257.09,-2344.17"/>
<polygon fill="black" stroke="black" points="257.09,-2344.13 253.05,-2338.16 257,-2332.13 261.05,-2338.1 257.09,-2344.13"/>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="6399.16,-88.5 6399.16,-146.9 6686.55,-146.9 6686.55,-88.5 6399.16,-88.5"/>
<text text-anchor="middle" x="6542.85" y="-130.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="6542.85" y="-113.5" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="6542.85" y="-96.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="6704.16,-88.5 6704.16,-146.9 6991.55,-146.9 6991.55,-88.5 6704.16,-88.5"/>
<text text-anchor="middle" x="6847.85" y="-130.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="6847.85" y="-113.5" font-family="Times,serif" font-size="14.00">IRMError</text>
<text text-anchor="middle" x="6847.85" y="-96.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="3066.16,-3122.9 3066.16,-3181.3 3353.55,-3181.3 3353.55,-3122.9 3066.16,-3122.9"/>
<text text-anchor="middle" x="3209.85" y="-3164.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="3209.85" y="-3147.9" font-family="Times,serif" font-size="14.00">RiskEngineError</text>
<text text-anchor="middle" x="3209.85" y="-3131.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="3066.16,-2729.9 3066.16,-2997.9 3353.55,-2997.9 3353.55,-2729.9 3066.16,-2729.9"/>
<text text-anchor="middle" x="3209.85" y="-2981.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="3209.85" y="-2964.5" font-family="Times,serif" font-size="14.00">Error</text>
<text text-anchor="middle" x="3209.85" y="-2947.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="3066.16,-2939.5 3353.55,-2939.5 "/>
<text text-anchor="start" x="3074.16" y="-2922.9" font-family="Times,serif" font-size="14.00">NO_ERROR: 0</text>
<text text-anchor="start" x="3074.16" y="-2906.1" font-family="Times,serif" font-size="14.00">RISKENGINE_MISMATCH: 1</text>
<text text-anchor="start" x="3074.16" y="-2889.3" font-family="Times,serif" font-size="14.00">INSUFFICIENT_SHORTFALL: 2</text>
<text text-anchor="start" x="3074.16" y="-2872.5" font-family="Times,serif" font-size="14.00">INSUFFICIENT_LIQUIDITY: 3</text>
<text text-anchor="start" x="3074.16" y="-2855.7" font-family="Times,serif" font-size="14.00">MARKET_NOT_LISTED: 4</text>
<text text-anchor="start" x="3074.16" y="-2838.9" font-family="Times,serif" font-size="14.00">PRICE_ERROR: 5</text>
<text text-anchor="start" x="3074.16" y="-2822.1" font-family="Times,serif" font-size="14.00">TOO_MUCH_REPAY: 6</text>
<text text-anchor="start" x="3074.16" y="-2805.3" font-family="Times,serif" font-size="14.00">SUPPLY_CAP_EXCEEDED: 7</text>
<text text-anchor="start" x="3074.16" y="-2788.5" font-family="Times,serif" font-size="14.00">BORROW_CAP_EXCEEDED: 8</text>
<text text-anchor="start" x="3074.16" y="-2771.7" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_AS_COLLATERAL: 9</text>
<text text-anchor="start" x="3074.16" y="-2754.9" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_TO_BORROW: 10</text>
<text text-anchor="start" x="3074.16" y="-2738.1" font-family="Times,serif" font-size="14.00">EMODE_NOT_ALLOWED: 11</text>
</g>
<!-- 12&#45;&gt;11 -->
<g id="edge3" class="edge">
<title>12&#45;&gt;11</title>
<path fill="none" stroke="black" d="M3209.85,-2997.95C3209.85,-3038.89 3209.85,-3080.94 3209.85,-3110.59"/>
<polygon fill="black" stroke="black" points="3209.85,-3110.9 3213.85,-3116.9 3209.85,-3122.9 3205.85,-3116.9 3209.85,-3110.9"/>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#f2f2f2" stroke="black" points="4212.63,-2055.3 4212.63,-2188.9 4853.08,-2188.9 4853.08,-2055.3 4212.63,-2055.3"/>
<text text-anchor="middle" x="4532.85" y="-2172.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4532.85" y="-2155.5" font-family="Times,serif" font-size="14.00">IInterestRateModel</text>
<text text-anchor="middle" x="4532.85" y="-2138.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="4212.63,-2130.5 4853.08,-2130.5 "/>
<text text-anchor="start" x="4220.63" y="-2113.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="4220.63" y="-2097.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getUtilization(cash: uint256, borrows: uint256, reserves: uint256): uint256</text>
<text text-anchor="start" x="4220.63" y="-2080.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getBorrowRate(cash: uint256, borrows: uint256, reserves: uint256): uint256</text>
<text text-anchor="start" x="4220.63" y="-2063.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getSupplyRate(cash: uint256, borrows: uint256, reserves: uint256, reserveFactorMantissa: uint256): uint256</text>
</g>
<!-- 14 -->
<g id="node15" class="node">
<title>14</title>
<polygon fill="#f2f2f2" stroke="black" points="5480.73,-759.1 5480.73,-1027.1 6074.98,-1027.1 6074.98,-759.1 5480.73,-759.1"/>
<text text-anchor="middle" x="5777.85" y="-1010.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5777.85" y="-993.7" font-family="Times,serif" font-size="14.00">IRBAC</text>
<text text-anchor="middle" x="5777.85" y="-976.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="5480.73,-968.7 6074.98,-968.7 "/>
<text text-anchor="start" x="5488.73" y="-952.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="5488.73" y="-935.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="5488.73" y="-918.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="5488.73" y="-901.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokePermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="5488.73" y="-884.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokeNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="5488.73" y="-868.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasPermission(permission: bytes32, target: address): bool</text>
<text text-anchor="start" x="5488.73" y="-851.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasNestedPermission(permission: bytes32, nestedAddress: address, target: address): bool</text>
<text text-anchor="start" x="5488.73" y="-834.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="5488.73" y="-817.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PermissionGranted(permission: bytes32, target: address)</text>
<text text-anchor="start" x="5488.73" y="-800.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NestedPermissionGranted(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="5488.73" y="-784.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PermissionRevoked(permission: bytes32, target: address)</text>
<text text-anchor="start" x="5488.73" y="-767.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NestedPermissionRevoked(permission: bytes32, nestedAddress: address, target: address)</text>
</g>
<!-- 15 -->
<g id="node16" class="node">
<title>15</title>
<polygon fill="#f2f2f2" stroke="black" points="5175.16,-813.9 5175.16,-972.3 5462.55,-972.3 5462.55,-813.9 5175.16,-813.9"/>
<text text-anchor="middle" x="5318.85" y="-955.7" font-family="Times,serif" font-size="14.00">DoubleJumpRateStorage</text>
<text text-anchor="middle" x="5318.85" y="-938.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="5175.16,-930.7 5462.55,-930.7 "/>
<text text-anchor="start" x="5183.16" y="-914.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="5183.16" y="-897.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_IRM_STORAGE: bytes32</text>
<text text-anchor="start" x="5183.16" y="-880.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;BASE: uint256</text>
<text text-anchor="start" x="5183.16" y="-863.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;SECONDS_PER_YEAR: uint256</text>
<polyline fill="none" stroke="black" points="5175.16,-855.5 5462.55,-855.5 "/>
<text text-anchor="start" x="5183.16" y="-838.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="5183.16" y="-822.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getIRMStorage(): (data: InterestRateData)</text>
</g>
<!-- 16 -->
<g id="node17" class="node">
<title>16</title>
<polygon fill="#f2f2f2" stroke="black" points="5175.16,-2038.5 5175.16,-2205.7 5462.55,-2205.7 5462.55,-2038.5 5175.16,-2038.5"/>
<text text-anchor="middle" x="5318.85" y="-2189.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="5318.85" y="-2172.3" font-family="Times,serif" font-size="14.00">InterestRateData</text>
<text text-anchor="middle" x="5318.85" y="-2155.5" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="5175.16,-2147.3 5462.55,-2147.3 "/>
<text text-anchor="start" x="5183.16" y="-2130.7" font-family="Times,serif" font-size="14.00">multiplierPerSecond: uint256</text>
<text text-anchor="start" x="5183.16" y="-2113.9" font-family="Times,serif" font-size="14.00">baseRatePerSecond: uint256</text>
<text text-anchor="start" x="5183.16" y="-2097.1" font-family="Times,serif" font-size="14.00">firstJumpMultiplierPerSecond: uint256</text>
<text text-anchor="start" x="5183.16" y="-2080.3" font-family="Times,serif" font-size="14.00">secondJumpMultiplierPerSecond: uint256</text>
<text text-anchor="start" x="5183.16" y="-2063.5" font-family="Times,serif" font-size="14.00">firstKink: uint256</text>
<text text-anchor="start" x="5183.16" y="-2046.7" font-family="Times,serif" font-size="14.00">secondKink: uint256</text>
</g>
<!-- 15&#45;&gt;16 -->
<g id="edge5" class="edge">
<title>15&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5315.33,-972.72C5311.12,-1189.53 5311.03,-1795.25 5315.07,-2028.19"/>
<polygon fill="black" stroke="black" points="5311.57,-2028.39 5315.25,-2038.33 5318.57,-2028.27 5311.57,-2028.39"/>
</g>
<!-- 16&#45;&gt;15 -->
<g id="edge4" class="edge">
<title>16&#45;&gt;15</title>
<path fill="none" stroke="black" d="M5322.46,-2038.33C5326.6,-1817.41 5326.65,-1215.05 5322.6,-985.03"/>
<polygon fill="black" stroke="black" points="5322.6,-984.72 5318.49,-978.79 5322.38,-972.72 5326.49,-978.65 5322.6,-984.72"/>
</g>
<!-- 17 -->
<g id="node18" class="node">
<title>17</title>
<polygon fill="#f2f2f2" stroke="black" points="3371.49,-713.1 3371.49,-1073.1 3890.21,-1073.1 3890.21,-713.1 3371.49,-713.1"/>
<text text-anchor="middle" x="3630.85" y="-1056.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="3630.85" y="-1039.7" font-family="Times,serif" font-size="14.00">RBACStorage</text>
<text text-anchor="middle" x="3630.85" y="-1022.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="3371.49,-1014.7 3890.21,-1014.7 "/>
<text text-anchor="start" x="3379.49" y="-998.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="3379.49" y="-981.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_RBAC_STORAGE: bytes32</text>
<text text-anchor="start" x="3379.49" y="-964.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3379.49" y="-947.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CONFIGURATOR_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-930.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PROTOCOL_OWNER_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-914.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_OWNER_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-897.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PAUSE_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-880.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_BORROW_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-863.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SUPPLY_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-846.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_MANAGER_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-830.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="3379.49" y="-813.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_EMERGENCY_WITHDRAWER_PERMISSION: bytes32</text>
<polyline fill="none" stroke="black" points="3371.49,-805.1 3890.21,-805.1 "/>
<text text-anchor="start" x="3379.49" y="-788.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3379.49" y="-771.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="3379.49" y="-754.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="3379.49" y="-738.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isPermissionValid(permission: bytes32)</text>
<text text-anchor="start" x="3379.49" y="-721.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getRBACStorage(): ($: RBACData)</text>
</g>
<!-- 18 -->
<g id="node19" class="node">
<title>18</title>
<polygon fill="#f2f2f2" stroke="black" points="3380.07,-2072.1 3380.07,-2172.1 3881.64,-2172.1 3881.64,-2072.1 3380.07,-2072.1"/>
<text text-anchor="middle" x="3630.85" y="-2155.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3630.85" y="-2138.7" font-family="Times,serif" font-size="14.00">RBACData</text>
<text text-anchor="middle" x="3630.85" y="-2121.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="3380.07,-2113.7 3881.64,-2113.7 "/>
<text text-anchor="start" x="3388.07" y="-2097.1" font-family="Times,serif" font-size="14.00">permissions: mapping(bytes32=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="3388.07" y="-2080.3" font-family="Times,serif" font-size="14.00">nestedPermissions: mapping(bytes32=&gt;mapping(address=&gt;mapping(address=&gt;bool)))</text>
</g>
<!-- 17&#45;&gt;18 -->
<g id="edge7" class="edge">
<title>17&#45;&gt;18</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3625.92,-1073.17C3623,-1354.09 3623.6,-1884.6 3627.74,-2061.79"/>
<polygon fill="black" stroke="black" points="3624.25,-2062.04 3627.99,-2071.95 3631.24,-2061.87 3624.25,-2062.04"/>
</g>
<!-- 18&#45;&gt;17 -->
<g id="edge6" class="edge">
<title>18&#45;&gt;17</title>
<path fill="none" stroke="black" d="M3633.71,-2071.95C3638.02,-1908.18 3638.75,-1375.08 3635.91,-1085.5"/>
<polygon fill="black" stroke="black" points="3635.91,-1085.17 3631.85,-1079.21 3635.79,-1073.17 3639.85,-1079.13 3635.91,-1085.17"/>
</g>
<!-- 19 -->
<g id="node20" class="node">
<title>19</title>
<polygon fill="#f2f2f2" stroke="black" points="1653.16,-2797.1 1653.16,-2930.7 1940.55,-2930.7 1940.55,-2797.1 1653.16,-2797.1"/>
<text text-anchor="middle" x="1796.85" y="-2914.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1796.85" y="-2897.3" font-family="Times,serif" font-size="14.00">IERC20Metadata</text>
<text text-anchor="middle" x="1796.85" y="-2880.5" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1653.16,-2872.3 1940.55,-2872.3 "/>
<text text-anchor="start" x="1661.16" y="-2855.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1661.16" y="-2838.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="1661.16" y="-2822.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="1661.16" y="-2805.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
</g>
<!-- 19&#45;&gt;3 -->
<g id="edge8" class="edge">
<title>19&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1796.85,-2930.92C1796.85,-2953.11 1796.85,-2978.82 1796.85,-3004.68"/>
<polygon fill="none" stroke="black" points="1786.35,-3004.89 1796.85,-3034.89 1807.35,-3004.89 1786.35,-3004.89"/>
</g>
<!-- 20 -->
<g id="node21" class="node">
<title>20</title>
<polygon fill="#f2f2f2" stroke="black" points="3908.51,-801.1 3908.51,-985.1 5157.2,-985.1 5157.2,-801.1 3908.51,-801.1"/>
<text text-anchor="middle" x="4532.85" y="-968.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4532.85" y="-951.7" font-family="Times,serif" font-size="14.00">IDoubleJumpRateModel</text>
<text text-anchor="middle" x="4532.85" y="-934.9" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="3908.51,-926.7 5157.2,-926.7 "/>
<text text-anchor="start" x="3916.51" y="-910.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3916.51" y="-893.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureInterestRateModel(baseRatePerYear: uint256, multiplierPerYear: uint256, firstJumpMultiplierPerYear: uint256, secondJumpMultiplierPerYear: uint256, firstKink: uint256, secondKink: uint256)</text>
<text text-anchor="start" x="3916.51" y="-876.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;kinks(): (uint256, uint256)</text>
<text text-anchor="start" x="3916.51" y="-859.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;baseRatePerSecond(): uint256</text>
<text text-anchor="start" x="3916.51" y="-842.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;multipliers(): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="3916.51" y="-826.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3916.51" y="-809.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewInterestParams(baseRatePerSecond: uint256, multiplierPerSecond: uint256, firstJumpMultiplierPerSecond: uint256, secondJumpMultiplierPerSecond: uint256, firstKink: uint256, secondKink: uint256)</text>
</g>
<!-- 20&#45;&gt;13 -->
<g id="edge9" class="edge">
<title>20&#45;&gt;13</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4532.85,-985.32C4532.85,-1211.03 4532.85,-1793.97 4532.85,-2024.87"/>
<polygon fill="none" stroke="black" points="4522.35,-2025.04 4532.85,-2055.04 4543.35,-2025.04 4522.35,-2025.04"/>
</g>
<!-- 21 -->
<g id="node22" class="node">
<title>21</title>
<polygon fill="#f2f2f2" stroke="black" points="1691.15,-1920.9 1691.15,-2323.3 2312.56,-2323.3 2312.56,-1920.9 1691.15,-1920.9"/>
<text text-anchor="middle" x="2001.85" y="-2306.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2001.85" y="-2289.9" font-family="Times,serif" font-size="14.00">IERC4626</text>
<text text-anchor="middle" x="2001.85" y="-2273.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1691.15,-2264.9 2312.56,-2264.9 "/>
<text text-anchor="start" x="1699.15" y="-2248.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1699.15" y="-2231.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): (assetTokenAddress: address)</text>
<text text-anchor="start" x="1699.15" y="-2214.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): (totalManagedAssets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2197.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2181.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2164.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(receiver: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2147.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2130.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(assets: uint256, receiver: address): (shares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2113.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2097.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2080.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(shares: uint256, receiver: address): (assets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2063.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2046.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2029.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): (shares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-2013.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-1996.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-1979.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): (assets: uint256)</text>
<text text-anchor="start" x="1699.15" y="-1962.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1699.15" y="-1945.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Deposit(sender: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1699.15" y="-1929.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Withdraw(sender: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
</g>
<!-- 21&#45;&gt;3 -->
<g id="edge10" class="edge">
<title>21&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2029.13,-2323.41C2045.32,-2507.29 2046.76,-2783.05 1949.85,-2998.4 1948.21,-3002.06 1946.43,-3005.68 1944.54,-3009.26"/>
<polygon fill="none" stroke="black" points="1935.56,-3003.82 1928.68,-3034.85 1953.41,-3014.88 1935.56,-3003.82"/>
</g>
<!-- 21&#45;&gt;19 -->
<g id="edge11" class="edge">
<title>21&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1946.38,-2323.3C1906.34,-2467.79 1854.08,-2656.38 1823.13,-2768.06"/>
<polygon fill="none" stroke="black" points="1812.98,-2765.37 1815.09,-2797.09 1833.22,-2770.98 1812.98,-2765.37"/>
</g>
<!-- 22 -->
<g id="node23" class="node">
<title>22</title>
<polygon fill="#f2f2f2" stroke="black" points="1076.76,-2013.3 1076.76,-2230.9 1596.95,-2230.9 1596.95,-2013.3 1076.76,-2013.3"/>
<text text-anchor="middle" x="1336.85" y="-2214.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1336.85" y="-2197.5" font-family="Times,serif" font-size="14.00">SafeERC20</text>
<text text-anchor="middle" x="1336.85" y="-2180.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1076.76,-2172.5 1596.95,-2172.5 "/>
<text text-anchor="start" x="1084.76" y="-2155.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1084.76" y="-2139.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturn(token: IERC20, data: bytes)</text>
<text text-anchor="start" x="1084.76" y="-2122.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturnBool(token: IERC20, data: bytes): bool</text>
<text text-anchor="start" x="1084.76" y="-2105.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1084.76" y="-2088.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransfer(token: IERC20, to: address, value: uint256)</text>
<text text-anchor="start" x="1084.76" y="-2071.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransferFrom(token: IERC20, from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1084.76" y="-2055.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeIncreaseAllowance(token: IERC20, spender: address, value: uint256)</text>
<text text-anchor="start" x="1084.76" y="-2038.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeDecreaseAllowance(token: IERC20, spender: address, requestedDecrease: uint256)</text>
<text text-anchor="start" x="1084.76" y="-2021.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;forceApprove(token: IERC20, spender: address, value: uint256)</text>
</g>
<!-- 22&#45;&gt;3 -->
<g id="edge13" class="edge">
<title>22&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1277.82,-2231.13C1216.84,-2349.4 1125.66,-2546.61 1089.85,-2729.4 1066.87,-2846.73 1014.77,-2905.36 1089.85,-2998.4 1149.87,-3072.76 1388.42,-3112.68 1572.01,-3132.82"/>
<polygon fill="black" stroke="black" points="1571.77,-3136.32 1582.09,-3133.91 1572.52,-3129.36 1571.77,-3136.32"/>
</g>
<!-- 22&#45;&gt;5 -->
<g id="edge12" class="edge">
<title>22&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1338.46,-2231.07C1340.45,-2365.31 1343.86,-2594.06 1345.97,-2736.38"/>
<polygon fill="black" stroke="black" points="1342.47,-2736.53 1346.12,-2746.48 1349.47,-2736.43 1342.47,-2736.53"/>
</g>
<!-- 23 -->
<g id="node24" class="node">
<title>23</title>
<polygon fill="#f2f2f2" stroke="black" points="598.86,-1874.9 598.86,-2369.3 1058.85,-2369.3 1058.85,-1874.9 598.86,-1874.9"/>
<text text-anchor="middle" x="828.85" y="-2352.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="828.85" y="-2335.9" font-family="Times,serif" font-size="14.00">ERC20</text>
<text text-anchor="middle" x="828.85" y="-2319.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="598.86,-2310.9 1058.85,-2310.9 "/>
<text text-anchor="start" x="606.86" y="-2294.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="606.86" y="-2277.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_balances: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="606.86" y="-2260.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_allowances: mapping(address=&gt;mapping(address=&gt;uint256))</text>
<text text-anchor="start" x="606.86" y="-2243.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_totalSupply: uint256</text>
<text text-anchor="start" x="606.86" y="-2227.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_name: string</text>
<text text-anchor="start" x="606.86" y="-2210.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_symbol: string</text>
<polyline fill="none" stroke="black" points="598.86,-2202.1 1058.85,-2202.1 "/>
<text text-anchor="start" x="606.86" y="-2185.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="606.86" y="-2168.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="606.86" y="-2151.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_update(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="606.86" y="-2135.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_mint(account: address, value: uint256)</text>
<text text-anchor="start" x="606.86" y="-2118.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_burn(account: address, value: uint256)</text>
<text text-anchor="start" x="606.86" y="-2101.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="606.86" y="-2084.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256, emitEvent: bool)</text>
<text text-anchor="start" x="606.86" y="-2067.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_spendAllowance(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="606.86" y="-2051.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="606.86" y="-2034.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(name_: string, symbol_: string)</text>
<text text-anchor="start" x="606.86" y="-2017.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="606.86" y="-2000.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="606.86" y="-1983.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="606.86" y="-1967.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="606.86" y="-1950.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="606.86" y="-1933.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="606.86" y="-1916.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="606.86" y="-1899.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="606.86" y="-1883.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
</g>
<!-- 23&#45;&gt;0 -->
<g id="edge17" class="edge">
<title>23&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M839.51,-2369.44C846.2,-2524.07 854.29,-2711.24 858.32,-2804.36"/>
<polygon fill="none" stroke="black" points="847.84,-2804.92 859.62,-2834.44 868.82,-2804.01 847.84,-2804.92"/>
</g>
<!-- 23&#45;&gt;3 -->
<g id="edge15" class="edge">
<title>23&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M914.67,-2369.44C949.92,-2478.58 988.35,-2609.23 1013.85,-2729.4 1026.36,-2788.31 1005.98,-2954.17 1046.85,-2998.4 1114.46,-3071.56 1360.15,-3111.4 1552.12,-3131.86"/>
<polygon fill="none" stroke="black" points="1551.22,-3142.32 1582.13,-3134.95 1553.37,-3121.43 1551.22,-3142.32"/>
</g>
<!-- 23&#45;&gt;6 -->
<g id="edge14" class="edge">
<title>23&#45;&gt;6</title>
<path fill="none" stroke="black" d="M737.95,-2369.44C687.45,-2506.3 627.54,-2668.65 590.61,-2768.72"/>
<polygon fill="none" stroke="black" points="580.68,-2765.29 580.15,-2797.07 600.38,-2772.56 580.68,-2765.29"/>
</g>
<!-- 23&#45;&gt;19 -->
<g id="edge16" class="edge">
<title>23&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M870.18,-2369.35C903.23,-2486.89 962.38,-2618.04 1067.85,-2693.4 1165.35,-2763.06 1492.83,-2689.62 1605.85,-2729.4 1638.3,-2740.82 1670.27,-2759.38 1698.49,-2779.08"/>
<polygon fill="none" stroke="black" points="1692.37,-2787.61 1722.76,-2796.93 1704.81,-2770.7 1692.37,-2787.61"/>
</g>
<!-- 24 -->
<g id="node25" class="node">
<title>24</title>
<polygon fill="#f2f2f2" stroke="black" points="1059.63,-603.9 1059.63,-1182.3 1614.08,-1182.3 1614.08,-603.9 1059.63,-603.9"/>
<text text-anchor="middle" x="1336.85" y="-1165.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1336.85" y="-1148.9" font-family="Times,serif" font-size="14.00">ERC4626</text>
<text text-anchor="middle" x="1336.85" y="-1132.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1059.63,-1123.9 1614.08,-1123.9 "/>
<text text-anchor="start" x="1067.63" y="-1107.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1067.63" y="-1090.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_asset: IERC20</text>
<text text-anchor="start" x="1067.63" y="-1073.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_underlyingDecimals: uint8</text>
<polyline fill="none" stroke="black" points="1059.63,-1065.5 1614.08,-1065.5 "/>
<text text-anchor="start" x="1067.63" y="-1048.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1067.63" y="-1032.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_tryGetAssetDecimals(asset_: IERC20): (bool, uint8)</text>
<text text-anchor="start" x="1067.63" y="-1015.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1067.63" y="-998.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToShares(assets: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="1067.63" y="-981.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToAssets(shares: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="1067.63" y="-964.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_deposit(caller: address, receiver: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1067.63" y="-948.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_withdraw(caller: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1067.63" y="-931.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_decimalsOffset(): uint8</text>
<text text-anchor="start" x="1067.63" y="-914.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1067.63" y="-897.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(asset_: IERC20)</text>
<text text-anchor="start" x="1067.63" y="-880.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="1067.63" y="-864.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="1067.63" y="-847.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="1067.63" y="-830.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="1067.63" y="-813.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="1067.63" y="-796.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxDeposit(address): uint256</text>
<text text-anchor="start" x="1067.63" y="-780.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxMint(address): uint256</text>
<text text-anchor="start" x="1067.63" y="-763.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="1067.63" y="-746.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxRedeem(owner: address): uint256</text>
<text text-anchor="start" x="1067.63" y="-729.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewDeposit(assets: uint256): uint256</text>
<text text-anchor="start" x="1067.63" y="-712.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewMint(shares: uint256): uint256</text>
<text text-anchor="start" x="1067.63" y="-696.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewWithdraw(assets: uint256): uint256</text>
<text text-anchor="start" x="1067.63" y="-679.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewRedeem(shares: uint256): uint256</text>
<text text-anchor="start" x="1067.63" y="-662.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;deposit(assets: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="1067.63" y="-645.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mint(shares: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="1067.63" y="-628.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="1067.63" y="-612.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): uint256</text>
</g>
<!-- 24&#45;&gt;3 -->
<g id="edge21" class="edge">
<title>24&#45;&gt;3</title>
<path fill="none" stroke="black" d="M1481.35,-1182.32C1530.47,-1294.48 1579.55,-1425.87 1605.85,-1550.8 1639,-1708.25 1580.26,-2850.6 1643.85,-2998.4 1647.96,-3007.94 1652.96,-3017.19 1658.62,-3026.12"/>
<polygon fill="black" stroke="black" points="1655.79,-3028.19 1664.24,-3034.58 1661.62,-3024.31 1655.79,-3028.19"/>
</g>
<!-- 24&#45;&gt;7 -->
<g id="edge20" class="edge">
<title>24&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1059.34,-1076.98C892.59,-1197.01 684.78,-1365.67 536.85,-1550.8 452.45,-1656.43 383.52,-1790.99 334.48,-1902.88"/>
<polygon fill="black" stroke="black" points="331.18,-1901.69 330.4,-1912.26 337.6,-1904.49 331.18,-1901.69"/>
</g>
<!-- 24&#45;&gt;8 -->
<g id="edge23" class="edge">
<title>24&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1059.49,-1058.07C892.3,-1173.19 692.44,-1343.4 589.85,-1550.8 477.01,-1778.94 666.71,-2492.18 510.85,-2693.4 479.87,-2733.4 446.73,-2704.21 402.85,-2729.4 374.27,-2745.81 346.22,-2768.13 322.15,-2789.82"/>
<polygon fill="black" stroke="black" points="319.5,-2787.5 314.48,-2796.83 324.22,-2792.67 319.5,-2787.5"/>
</g>
<!-- 24&#45;&gt;19 -->
<g id="edge22" class="edge">
<title>24&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1499.37,-1182.7C1554.09,-1294.22 1608.84,-1425.08 1638.85,-1550.8 1697.85,-1797.95 1602.68,-2451.96 1681.85,-2693.4 1693,-2727.38 1713.13,-2760.83 1733.48,-2788.91"/>
<polygon fill="black" stroke="black" points="1730.74,-2791.1 1739.5,-2797.06 1736.38,-2786.94 1730.74,-2791.1"/>
</g>
<!-- 24&#45;&gt;21 -->
<g id="edge19" class="edge">
<title>24&#45;&gt;21</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1493.19,-1182.55C1610.95,-1399.84 1770.17,-1693.63 1878.92,-1894.27"/>
<polygon fill="none" stroke="black" points="1869.72,-1899.34 1893.25,-1920.71 1888.19,-1889.33 1869.72,-1899.34"/>
</g>
<!-- 24&#45;&gt;22 -->
<g id="edge24" class="edge">
<title>24&#45;&gt;22</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1336.85,-1182.55C1336.85,-1443.76 1336.85,-1815.49 1336.85,-2003.1"/>
<polygon fill="black" stroke="black" points="1333.35,-2003.26 1336.85,-2013.26 1340.35,-2003.26 1333.35,-2003.26"/>
</g>
<!-- 24&#45;&gt;23 -->
<g id="edge18" class="edge">
<title>24&#45;&gt;23</title>
<path fill="none" stroke="black" d="M1217.43,-1182.55C1134.35,-1383.21 1024.27,-1649.1 942.49,-1846.63"/>
<polygon fill="none" stroke="black" points="932.66,-1842.91 930.89,-1874.65 952.07,-1850.95 932.66,-1842.91"/>
</g>
<!-- 25 -->
<g id="node26" class="node">
<title>25</title>
<polygon fill="#f2f2f2" stroke="black" points="1927.11,-271.9 1927.11,-1514.3 2754.6,-1514.3 2754.6,-271.9 1927.11,-271.9"/>
<text text-anchor="middle" x="2340.85" y="-1497.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2340.85" y="-1480.9" font-family="Times,serif" font-size="14.00">IPToken</text>
<text text-anchor="middle" x="2340.85" y="-1464.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="1927.11,-1455.9 2754.6,-1455.9 "/>
<text text-anchor="start" x="1935.11" y="-1439.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1935.11" y="-1422.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(tokenAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-1405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(mintAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-1388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(redeemTokens: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-1372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(redeemAmount: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-1355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrow(borrowAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1338.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowOnBehalfOf(onBehalfOf: address, borrowAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1321.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrow(repayAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1304.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowOnBehalfOf(onBehalfOf: address, repayAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1288.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrow(borrower: address, repayAmount: uint256, pTokenCollateral: IPToken)</text>
<text text-anchor="start" x="1935.11" y="-1271.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrueInterest()</text>
<text text-anchor="start" x="1935.11" y="-1254.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seize(liquidator: address, borrower: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1237.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;addReserves(addAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1220.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveFactor(newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1204.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowRateMax(newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1187.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setProtocolSeizeShare(newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1170.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesEmergency(reduceAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1153.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesOwner(reduceAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1136.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesConfigurator(reduceAmount: uint256)</text>
<text text-anchor="start" x="1935.11" y="-1120.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;sweepToken(token: IERC20)</text>
<text text-anchor="start" x="1935.11" y="-1103.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRateMaxMantissa(): uint256</text>
<text text-anchor="start" x="1935.11" y="-1086.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrualBlockTimestamp(): uint256</text>
<text text-anchor="start" x="1935.11" y="-1069.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateCurrent(): uint256</text>
<text text-anchor="start" x="1935.11" y="-1052.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOfUnderlying(owner: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-1036.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountSnapshot(account: address): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="1935.11" y="-1019.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrowsCurrent(): uint256</text>
<text text-anchor="start" x="1935.11" y="-1002.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReservesCurrent(): uint256</text>
<text text-anchor="start" x="1935.11" y="-985.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReservesCurrent(): uint256</text>
<text text-anchor="start" x="1935.11" y="-968.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReservesCurrent(): uint256</text>
<text text-anchor="start" x="1935.11" y="-952.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceCurrent(account: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-935.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceStored(account: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-918.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateStored(): uint256</text>
<text text-anchor="start" x="1935.11" y="-901.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getCash(): uint256</text>
<text text-anchor="start" x="1935.11" y="-884.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRatePerSecond(): uint256</text>
<text text-anchor="start" x="1935.11" y="-868.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyRatePerSecond(): uint256</text>
<text text-anchor="start" x="1935.11" y="-851.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrows(): uint256</text>
<text text-anchor="start" x="1935.11" y="-834.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReserves(): uint256</text>
<text text-anchor="start" x="1935.11" y="-817.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReserves(): uint256</text>
<text text-anchor="start" x="1935.11" y="-800.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReserves(): uint256</text>
<text text-anchor="start" x="1935.11" y="-784.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowIndex(): uint256</text>
<text text-anchor="start" x="1935.11" y="-767.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reserveFactorMantissa(): uint256</text>
<text text-anchor="start" x="1935.11" y="-750.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;riskEngine(): IRiskEngine</text>
<text text-anchor="start" x="1935.11" y="-733.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="1935.11" y="-716.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="1935.11" y="-700.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="1935.11" y="-683.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="1935.11" y="-666.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="1935.11" y="-649.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;protocolSeizeShareMantissa(): uint256</text>
<text text-anchor="start" x="1935.11" y="-632.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="1935.11" y="-616.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="1935.11" y="-599.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-582.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(account: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-565.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1935.11" y="-548.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="1935.11" y="-532.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1935.11" y="-515.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1935.11" y="-498.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1935.11" y="-481.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1935.11" y="-464.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="1935.11" y="-448.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1935.11" y="-431.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewRiskEngine(oldRiskEngine: IRiskEngine, newRiskEngine: IRiskEngine)</text>
<text text-anchor="start" x="1935.11" y="-414.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Borrow(borrower: address, onBehalfOf: address, borrowAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="1935.11" y="-397.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RepayBorrow(payer: address, onBehalfOf: address, repayAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="1935.11" y="-380.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveFactor(oldReserveFactorMantissa: uint256, newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="1935.11" y="-364.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowRateMax(oldBorrowRateMaxMantissa: uint256, newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="1935.11" y="-347.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewProtocolSeizeShare(oldProtocolSeizeShareMantissa: uint256, newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="1935.11" y="-330.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AccrueInterest(cashPrior: uint256, totalReserves: uint256, borrowIndex: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="1935.11" y="-313.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; LiquidateBorrow(liquidator: address, borrower: address, repayAmount: uint256, pTokenCollateral: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="1935.11" y="-296.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesAdded(benefactor: address, addAmount: uint256, newTotalReserves: uint256)</text>
<text text-anchor="start" x="1935.11" y="-280.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesReduced(admin: address, reduceAmount: uint256, newTotalReserves: uint256)</text>
</g>
<!-- 25&#45;&gt;3 -->
<g id="edge28" class="edge">
<title>25&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2345.74,-1514.6C2347.85,-2013.08 2345.63,-2638.92 2321.85,-2693.4 2259.8,-2835.58 2128.08,-2950.21 2012.12,-3029.19"/>
<polygon fill="black" stroke="black" points="2010.12,-3026.31 2003.79,-3034.81 2014.04,-3032.11 2010.12,-3026.31"/>
</g>
<!-- 25&#45;&gt;21 -->
<g id="edge25" class="edge">
<title>25&#45;&gt;21</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2169.49,-1514.34C2132.2,-1649.3 2095.04,-1783.83 2065.21,-1891.79"/>
<polygon fill="none" stroke="black" points="2055.06,-1889.09 2057.19,-1920.8 2075.3,-1894.68 2055.06,-1889.09"/>
</g>
<!-- 25&#45;&gt;25 -->
<g id="edge27" class="edge">
<title>25&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2754.76,-819.32C2766.17,-839.6 2772.6,-864.19 2772.6,-893.1 2772.6,-917.83 2767.9,-939.4 2759.39,-957.81"/>
<polygon fill="black" stroke="black" points="2756.2,-956.38 2754.76,-966.88 2762.43,-959.56 2756.2,-956.38"/>
</g>
<!-- 26 -->
<g id="node27" class="node">
<title>26</title>
<polygon fill="#f2f2f2" stroke="black" points="2369.32,-1551.3 2369.32,-2692.9 3362.39,-2692.9 3362.39,-1551.3 2369.32,-1551.3"/>
<text text-anchor="middle" x="2865.85" y="-2676.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2865.85" y="-2659.5" font-family="Times,serif" font-size="14.00">IRiskEngine</text>
<text text-anchor="middle" x="2865.85" y="-2642.7" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="2369.32,-2634.5 3362.39,-2634.5 "/>
<text text-anchor="start" x="2377.32" y="-2617.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2377.32" y="-2601.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="2377.32" y="-2584.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;switchEMode(newCategoryId: uint8)</text>
<text text-anchor="start" x="2377.32" y="-2567.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;enterMarkets(pTokens: address[]): uint256[]</text>
<text text-anchor="start" x="2377.32" y="-2550.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exitMarket(pTokenAddress: address)</text>
<text text-anchor="start" x="2377.32" y="-2533.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;updateDelegate(delegate: address, approved: bool)</text>
<text text-anchor="start" x="2377.32" y="-2517.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintVerify(account: address)</text>
<text text-anchor="start" x="2377.32" y="-2500.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowVerify(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="2377.32" y="-2483.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowAllowed(pToken: address, borrower: address, borrowAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-2466.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setOracle(newOracle: address)</text>
<text text-anchor="start" x="2377.32" y="-2449.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureEMode(categoryId: uint8, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="2377.32" y="-2433.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setCloseFactor(pTokenAddress: address, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="2377.32" y="-2416.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureMarket(pToken: IPToken, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="2377.32" y="-2399.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportMarket(pToken: IPToken)</text>
<text text-anchor="start" x="2377.32" y="-2382.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportEMode(categoryId: uint8, isAllowed: bool, pTokens: address[], collateralPermissions: bool[], borrowPermissions: bool[])</text>
<text text-anchor="start" x="2377.32" y="-2365.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketBorrowCaps(pTokens: IPToken[], newBorrowCaps: uint256[])</text>
<text text-anchor="start" x="2377.32" y="-2349.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketSupplyCaps(pTokens: IPToken[], newSupplyCaps: uint256[])</text>
<text text-anchor="start" x="2377.32" y="-2332.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMintPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="2377.32" y="-2315.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="2377.32" y="-2298.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setTransferPaused(state: bool): bool</text>
<text text-anchor="start" x="2377.32" y="-2281.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setSeizePaused(state: bool): bool</text>
<text text-anchor="start" x="2377.32" y="-2265.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAssetsIn(account: address): IPToken[]</text>
<text text-anchor="start" x="2377.32" y="-2248.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getReserveShares(): (ownerShareMantissa: uint256, configuratorShareMantissa: uint256)</text>
<text text-anchor="start" x="2377.32" y="-2231.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkCollateralMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="2377.32" y="-2214.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkBorrowMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="2377.32" y="-2197.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accountCategory(account: address): uint8</text>
<text text-anchor="start" x="2377.32" y="-2181.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="2377.32" y="-2164.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountBorrowLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="2377.32" y="-2147.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getHypotheticalAccountLiquidity(account: address, pTokenModify: address, redeemTokens: uint256, borrowAmount: uint256): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="2377.32" y="-2130.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateCalculateSeizeTokens(borrower: address, pTokenBorrowed: address, pTokenCollateral: address, actualRepayAmount: uint256): (RiskEngineError.Error, uint256)</text>
<text text-anchor="start" x="2377.32" y="-2113.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;delegateAllowed(user: address, delegate: address): bool</text>
<text text-anchor="start" x="2377.32" y="-2097.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAllMarkets(): IPToken[]</text>
<text text-anchor="start" x="2377.32" y="-2080.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;isDeprecated(pToken: IPToken): bool</text>
<text text-anchor="start" x="2377.32" y="-2063.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(pToken: address, account: address): uint256</text>
<text text-anchor="start" x="2377.32" y="-2046.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintAllowed(account: address, pToken: address, mintAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-2029.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeemAllowed(pToken: address, redeemer: address, redeemTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-2013.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowAllowed(pToken: address): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-1996.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrowAllowed(pTokenBorrowed: address, pTokenCollateral: address, borrower: address, repayAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-1979.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seizeAllowed(pTokenCollateral: address, pTokenBorrowed: address): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-1962.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferAllowed(pToken: address, src: address, transferTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="2377.32" y="-1945.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;oracle(): address</text>
<text text-anchor="start" x="2377.32" y="-1929.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;collateralFactor(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="2377.32" y="-1912.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationThreshold(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="2377.32" y="-1895.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationIncentive(categoryId: uint8, pToken: address): uint256</text>
<text text-anchor="start" x="2377.32" y="-1878.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;closeFactor(pToken: address): uint256</text>
<text text-anchor="start" x="2377.32" y="-1861.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyCap(pToken: address): uint256</text>
<text text-anchor="start" x="2377.32" y="-1845.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowCap(pToken: address): uint256</text>
<text text-anchor="start" x="2377.32" y="-1828.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;emodeMarkets(categoryId: uint8): (collateralTokens: address[], borrowTokens: address[])</text>
<text text-anchor="start" x="2377.32" y="-1811.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2377.32" y="-1794.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewEModeConfiguration(categoryId: uint8, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="2377.32" y="-1777.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewMarketConfiguration(pToken: IPToken, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="2377.32" y="-1761.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewOracleEngine(oldOracleEngine: address, newOracleEngine: address)</text>
<text text-anchor="start" x="2377.32" y="-1744.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="2377.32" y="-1727.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketListed(pToken: IPToken)</text>
<text text-anchor="start" x="2377.32" y="-1710.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeSwitched(account: address, oldCategory: uint8, newCategory: uint8)</text>
<text text-anchor="start" x="2377.32" y="-1693.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeUpdated(categoryId: uint8, pToken: address, allowed: bool, collateralStatus: bool, borrowStatus: bool)</text>
<text text-anchor="start" x="2377.32" y="-1677.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketEntered(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="2377.32" y="-1660.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketExited(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="2377.32" y="-1643.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewCloseFactor(pToken: address, oldCloseFactorMantissa: uint256, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="2377.32" y="-1626.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(action: string, pauseState: bool)</text>
<text text-anchor="start" x="2377.32" y="-1609.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(pToken: IPToken, action: string, pauseState: bool)</text>
<text text-anchor="start" x="2377.32" y="-1593.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowCap(pToken: IPToken, newBorrowCap: uint256)</text>
<text text-anchor="start" x="2377.32" y="-1576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewSupplyCap(pToken: IPToken, newSupplyCap: uint256)</text>
<text text-anchor="start" x="2377.32" y="-1559.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; DelegateUpdated(approver: address, delegate: address, approved: bool)</text>
</g>
<!-- 25&#45;&gt;26 -->
<g id="edge26" class="edge">
<title>25&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2599.49,-1514.34C2603.36,-1523.39 2607.23,-1532.43 2611.11,-1541.47"/>
<polygon fill="black" stroke="black" points="2607.98,-1543.07 2615.14,-1550.88 2614.42,-1540.31 2607.98,-1543.07"/>
</g>
<!-- 26&#45;&gt;11 -->
<g id="edge32" class="edge">
<title>26&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3011.54,-2693.13C3014.06,-2705.31 3016.5,-2717.42 3018.85,-2729.4 3042.1,-2847.88 3002.89,-2890.39 3056.85,-2998.4 3080.92,-3046.57 3126.08,-3088.72 3161.12,-3116.46"/>
<polygon fill="black" stroke="black" points="3159.3,-3119.47 3169.34,-3122.85 3163.59,-3113.95 3159.3,-3119.47"/>
</g>
<!-- 26&#45;&gt;12 -->
<g id="edge33" class="edge">
<title>26&#45;&gt;12</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3130.93,-2693.17C3135.31,-2702.59 3139.57,-2711.75 3143.7,-2720.63"/>
<polygon fill="black" stroke="black" points="3140.56,-2722.19 3147.95,-2729.78 3146.91,-2719.23 3140.56,-2722.19"/>
</g>
<!-- 26&#45;&gt;25 -->
<g id="edge31" class="edge">
<title>26&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2628.61,-1550.88C2624.75,-1541.86 2620.89,-1532.81 2617.02,-1523.77"/>
<polygon fill="black" stroke="black" points="2620.14,-1522.16 2612.99,-1514.34 2613.7,-1524.91 2620.14,-1522.16"/>
</g>
<!-- 27 -->
<g id="node28" class="node">
<title>27</title>
<polygon fill="#f2f2f2" stroke="black" points="2722.16,-2805.5 2722.16,-2922.3 3009.55,-2922.3 3009.55,-2805.5 2722.16,-2805.5"/>
<text text-anchor="middle" x="2865.85" y="-2905.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2865.85" y="-2888.9" font-family="Times,serif" font-size="14.00">BaseConfiguration</text>
<text text-anchor="middle" x="2865.85" y="-2872.1" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="2722.16,-2863.9 3009.55,-2863.9 "/>
<text text-anchor="start" x="2730.16" y="-2847.3" font-family="Times,serif" font-size="14.00">collateralFactorMantissa: uint256</text>
<text text-anchor="start" x="2730.16" y="-2830.5" font-family="Times,serif" font-size="14.00">liquidationThresholdMantissa: uint256</text>
<text text-anchor="start" x="2730.16" y="-2813.7" font-family="Times,serif" font-size="14.00">liquidationIncentiveMantissa: uint256</text>
</g>
<!-- 26&#45;&gt;27 -->
<g id="edge30" class="edge">
<title>26&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2860.07,-2693.17C2860.49,-2732.36 2861.05,-2767.26 2861.75,-2795.07"/>
<polygon fill="black" stroke="black" points="2858.26,-2795.47 2862.03,-2805.37 2865.26,-2795.28 2858.26,-2795.47"/>
</g>
<!-- 27&#45;&gt;26 -->
<g id="edge29" class="edge">
<title>27&#45;&gt;26</title>
<path fill="none" stroke="black" d="M2869.68,-2805.37C2870.43,-2778.79 2871.04,-2744.4 2871.5,-2705.2"/>
<polygon fill="black" stroke="black" points="2871.5,-2705.17 2867.57,-2699.13 2871.63,-2693.17 2875.57,-2699.22 2871.5,-2705.17"/>
</g>
<!-- 28 -->
<g id="node29" class="node">
<title>28</title>
<polygon fill="#f2f2f2" stroke="black" points="3945.14,-0.5 3945.14,-234.9 5120.57,-234.9 5120.57,-0.5 3945.14,-0.5"/>
<text text-anchor="middle" x="4532.85" y="-218.3" font-family="Times,serif" font-size="14.00">DoubleJumpRateModel</text>
<text text-anchor="middle" x="4532.85" y="-201.5" font-family="Times,serif" font-size="14.00">public/flatten/DoubleJumpRateModel.flatten.sol</text>
<polyline fill="none" stroke="black" points="3945.14,-193.3 5120.57,-193.3 "/>
<text text-anchor="start" x="3953.14" y="-176.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3953.14" y="-159.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="3953.14" y="-143.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3953.14" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;configureInterestRateModel(baseRatePerYear: uint256, multiplierPerYear: uint256, firstJumpMultiplierPerYear: uint256, secondJumpMultiplierPerYear: uint256, firstKink: uint256, secondKink: uint256)</text>
<text text-anchor="start" x="3953.14" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;kinks(): (uint256, uint256)</text>
<text text-anchor="start" x="3953.14" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;baseRatePerSecond(): uint256</text>
<text text-anchor="start" x="3953.14" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;multipliers(): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="3953.14" y="-59.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3953.14" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getSupplyRate(cash: uint256, borrows: uint256, reserves: uint256, reserveFactorMantissa: uint256): uint256</text>
<text text-anchor="start" x="3953.14" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBorrowRate(cash: uint256, borrows: uint256, reserves: uint256): uint256</text>
<text text-anchor="start" x="3953.14" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getUtilization(cash: uint256, borrows: uint256, reserves: uint256): uint256</text>
</g>
<!-- 28&#45;&gt;14 -->
<g id="edge38" class="edge">
<title>28&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5120.62,-234.16C5123.37,-234.57 5126.12,-234.99 5128.85,-235.4 5204.65,-246.77 5408.98,-227.58 5471.85,-271.4 5632.04,-383.04 5712.17,-603.08 5749.32,-748.9"/>
<polygon fill="black" stroke="black" points="5745.99,-749.99 5751.82,-758.83 5752.78,-748.28 5745.99,-749.99"/>
</g>
<!-- 28&#45;&gt;15 -->
<g id="edge35" class="edge">
<title>28&#45;&gt;15</title>
<path fill="none" stroke="black" d="M5108.06,-235.01C5132.91,-246.31 5152.83,-258.44 5165.85,-271.4 5302.76,-407.63 5324.24,-645.46 5323.95,-783.81"/>
<polygon fill="none" stroke="black" points="5313.45,-783.69 5323.54,-813.83 5334.45,-783.97 5313.45,-783.69"/>
</g>
<!-- 28&#45;&gt;16 -->
<g id="edge37" class="edge">
<title>28&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5120.59,-234.43C5123.35,-234.76 5126.1,-235.08 5128.85,-235.4 5181.59,-241.52 6047.02,-233.16 6083.85,-271.4 6467.19,-669.45 6324.38,-1017.27 6083.85,-1514.8 5957.13,-1776.94 5655.93,-1960.31 5471.53,-2052.56"/>
<polygon fill="black" stroke="black" points="5469.96,-2049.44 5462.56,-2057.02 5473.07,-2055.7 5469.96,-2049.44"/>
</g>
<!-- 28&#45;&gt;17 -->
<g id="edge36" class="edge">
<title>28&#45;&gt;17</title>
<path fill="none" stroke="black" d="M3966.57,-234.9C3938.15,-246.39 3915.1,-258.58 3899.85,-271.4 3776.13,-375.46 3707.69,-547.07 3670.84,-683.5"/>
<polygon fill="none" stroke="black" points="3660.58,-681.25 3663.24,-712.92 3680.91,-686.51 3660.58,-681.25"/>
</g>
<!-- 28&#45;&gt;20 -->
<g id="edge34" class="edge">
<title>28&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4532.85,-234.9C4532.85,-379.24 4532.85,-624.29 4532.85,-770.9"/>
<polygon fill="none" stroke="black" points="4522.35,-771.06 4532.85,-801.06 4543.35,-771.06 4522.35,-771.06"/>
</g>
<!-- 28&#45;&gt;25 -->
<g id="edge39" class="edge">
<title>28&#45;&gt;25</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3944.87,-147.35C3755.19,-169.2 3546.58,-206.87 3362.85,-271.4 3153.18,-345.05 2940.16,-465.64 2763.25,-581.03"/>
<polygon fill="black" stroke="black" points="2761.3,-578.12 2754.85,-586.53 2765.13,-583.98 2761.3,-578.12"/>
</g>
</g>
</svg>
