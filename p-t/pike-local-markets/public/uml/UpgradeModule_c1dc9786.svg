<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="1078pt" height="576pt"
 viewBox="0.00 0.00 1078.10 575.80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 571.8)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-571.8 1074.1,-571.8 1074.1,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="82.19,-71.7 82.19,-130.1 327.57,-130.1 327.57,-71.7 82.19,-71.7"/>
<text text-anchor="middle" x="204.88" y="-113.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="204.88" y="-96.7" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="204.88" y="-79.9" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-250.7 0,-417.9 373.76,-417.9 373.76,-250.7 0,-250.7"/>
<text text-anchor="middle" x="186.88" y="-401.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="186.88" y="-384.5" font-family="Times,serif" font-size="14.00">IUpgrade</text>
<text text-anchor="middle" x="186.88" y="-367.7" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-359.5 373.76,-359.5 "/>
<text text-anchor="start" x="8" y="-342.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="8" y="-326.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;upgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="8" y="-309.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;simulateUpgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="8" y="-292.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getImplementation(): address</text>
<text text-anchor="start" x="8" y="-275.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="8" y="-258.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Upgraded(self: address, implementation: address)</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="391.37,-238.3 391.37,-430.3 704.39,-430.3 704.39,-238.3 391.37,-238.3"/>
<text text-anchor="middle" x="547.88" y="-413.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="547.88" y="-396.9" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="547.88" y="-380.1" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="391.37,-371.9 704.39,-371.9 "/>
<text text-anchor="start" x="399.37" y="-355.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="399.37" y="-338.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="391.37,-330.3 704.39,-330.3 "/>
<text text-anchor="start" x="399.37" y="-313.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="399.37" y="-296.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="399.37" y="-280.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="399.37" y="-263.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="399.37" y="-246.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="425.19,-467.3 425.19,-567.3 670.57,-567.3 670.57,-467.3 425.19,-467.3"/>
<text text-anchor="middle" x="547.88" y="-550.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="547.88" y="-533.9" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="547.88" y="-517.1" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="425.19,-508.9 670.57,-508.9 "/>
<text text-anchor="start" x="433.19" y="-492.3" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="433.19" y="-475.5" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 2&#45;&gt;3 -->
<g id="edge2" class="edge">
<title>2&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M541.14,-430.31C541.17,-439.29 541.29,-448.21 541.49,-456.71"/>
<polygon fill="black" stroke="black" points="538,-456.97 541.79,-466.86 545,-456.76 538,-456.97"/>
</g>
<!-- 3&#45;&gt;2 -->
<g id="edge1" class="edge">
<title>3&#45;&gt;2</title>
<path fill="none" stroke="black" d="M553.97,-466.86C554.24,-459.11 554.42,-450.84 554.53,-442.38"/>
<polygon fill="black" stroke="black" points="554.53,-442.31 550.58,-436.27 554.62,-430.31 558.57,-436.34 554.53,-442.31"/>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="722.81,-271.9 722.81,-396.7 1024.95,-396.7 1024.95,-271.9 722.81,-271.9"/>
<text text-anchor="middle" x="873.88" y="-380.1" font-family="Times,serif" font-size="14.00">UpgradeStorage</text>
<text text-anchor="middle" x="873.88" y="-363.3" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="722.81,-355.1 1024.95,-355.1 "/>
<text text-anchor="start" x="730.81" y="-338.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="730.81" y="-321.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_IMPLEMENTATION_SLOT: bytes32</text>
<polyline fill="none" stroke="black" points="722.81,-313.5 1024.95,-313.5 "/>
<text text-anchor="start" x="730.81" y="-296.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="730.81" y="-280.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getImplementationStorage(): (data: ProxyData)</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="751.19,-467.3 751.19,-567.3 996.57,-567.3 996.57,-467.3 751.19,-467.3"/>
<text text-anchor="middle" x="873.88" y="-550.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="873.88" y="-533.9" font-family="Times,serif" font-size="14.00">ProxyData</text>
<text text-anchor="middle" x="873.88" y="-517.1" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="751.19,-508.9 996.57,-508.9 "/>
<text text-anchor="start" x="759.19" y="-492.3" font-family="Times,serif" font-size="14.00">implementation: address</text>
<text text-anchor="start" x="759.19" y="-475.5" font-family="Times,serif" font-size="14.00">simulatingUpgrade: bool</text>
</g>
<!-- 4&#45;&gt;5 -->
<g id="edge4" class="edge">
<title>4&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M867.45,-396.83C867.01,-416.29 867.02,-437.75 867.5,-457.04"/>
<polygon fill="black" stroke="black" points="864.01,-457.32 867.8,-467.21 871,-457.12 864.01,-457.32"/>
</g>
<!-- 5&#45;&gt;4 -->
<g id="edge3" class="edge">
<title>5&#45;&gt;4</title>
<path fill="none" stroke="black" d="M879.96,-467.21C880.58,-449.25 880.77,-428.51 880.52,-408.88"/>
<polygon fill="black" stroke="black" points="880.52,-408.83 876.42,-402.9 880.31,-396.83 884.42,-402.76 880.52,-408.83"/>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="345.49,-0.5 345.49,-201.3 750.27,-201.3 750.27,-0.5 345.49,-0.5"/>
<text text-anchor="middle" x="547.88" y="-184.7" font-family="Times,serif" font-size="14.00">UpgradeModule</text>
<text text-anchor="middle" x="547.88" y="-167.9" font-family="Times,serif" font-size="14.00">public/flatten/UpgradeModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="345.49,-159.7 750.27,-159.7 "/>
<text text-anchor="start" x="353.49" y="-143.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="353.49" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_upgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="353.49" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_implementationIsSterile(candidateImplementation: address): bool</text>
<text text-anchor="start" x="353.49" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;isContract(account: address): bool</text>
<text text-anchor="start" x="353.49" y="-75.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="353.49" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getImplementation(): address</text>
<text text-anchor="start" x="353.49" y="-42.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="353.49" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;upgradeTo(newImplementation: address)</text>
<text text-anchor="start" x="353.49" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;simulateUpgradeTo(newImplementation: address)</text>
</g>
<!-- 6&#45;&gt;1 -->
<g id="edge5" class="edge">
<title>6&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M392.69,-201.38C375.68,-212.28 358.37,-223.37 341.38,-234.27"/>
<polygon fill="none" stroke="black" points="335.49,-225.57 315.9,-250.6 346.83,-243.25 335.49,-225.57"/>
</g>
<!-- 6&#45;&gt;2 -->
<g id="edge7" class="edge">
<title>6&#45;&gt;2</title>
<path fill="none" stroke="black" d="M547.88,-201.38C547.88,-203.56 547.88,-205.74 547.88,-207.93"/>
<polygon fill="none" stroke="black" points="537.38,-207.94 547.88,-237.94 558.38,-207.94 537.38,-207.94"/>
</g>
<!-- 6&#45;&gt;4 -->
<g id="edge6" class="edge">
<title>6&#45;&gt;4</title>
<path fill="none" stroke="black" d="M688.02,-201.38C712.87,-219.01 738.44,-237.16 762.36,-254.14"/>
<polygon fill="none" stroke="black" points="756.52,-262.88 787.07,-271.68 768.68,-245.75 756.52,-262.88"/>
</g>
<!-- 6&#45;&gt;5 -->
<g id="edge8" class="edge">
<title>6&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M750.29,-133.34C867.39,-156.12 998.46,-191.12 1033.88,-237.8 1085.73,-306.13 1077.98,-357.23 1033.88,-430.8 1026.38,-443.31 1016.36,-454.09 1004.97,-463.36"/>
<polygon fill="black" stroke="black" points="1002.7,-460.69 996.88,-469.54 1006.95,-466.25 1002.7,-460.69"/>
</g>
</g>
</svg>
