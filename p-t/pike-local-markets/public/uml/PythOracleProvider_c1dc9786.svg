<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="3770pt" height="1647pt"
 viewBox="0.00 0.00 3770.31 1646.60" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1642.6)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-1642.6 3766.31,-1642.6 3766.31,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="2007.27,-899.3 2007.27,-999.3 2272.09,-999.3 2272.09,-899.3 2007.27,-899.3"/>
<text text-anchor="middle" x="2139.68" y="-982.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2139.68" y="-965.9" font-family="Times,serif" font-size="14.00">IERC1822Proxiable</text>
<text text-anchor="middle" x="2139.68" y="-949.1" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2007.27,-940.9 2272.09,-940.9 "/>
<text text-anchor="start" x="2015.27" y="-924.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2015.27" y="-907.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;proxiableUUID(): bytes32</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="2062.27,-1296.3 2062.27,-1396.3 2327.09,-1396.3 2327.09,-1296.3 2062.27,-1296.3"/>
<text text-anchor="middle" x="2194.68" y="-1379.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2194.68" y="-1362.9" font-family="Times,serif" font-size="14.00">IBeacon</text>
<text text-anchor="middle" x="2194.68" y="-1346.1" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2062.27,-1337.9 2327.09,-1337.9 "/>
<text text-anchor="start" x="2070.27" y="-1321.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2070.27" y="-1304.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;implementation(): address</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-832.1 0,-1066.5 429.36,-1066.5 429.36,-832.1 0,-832.1"/>
<text text-anchor="middle" x="214.68" y="-1049.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="214.68" y="-1033.1" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="214.68" y="-1016.3" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-1008.1 429.36,-1008.1 "/>
<text text-anchor="start" x="8" y="-991.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="8" y="-974.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="8" y="-957.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="8" y="-941.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="8" y="-924.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="8" y="-907.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="8" y="-890.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
<text text-anchor="start" x="8" y="-873.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="8" y="-857.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="8" y="-840.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="1232.25,-1229.1 1232.25,-1463.5 1731.12,-1463.5 1731.12,-1229.1 1232.25,-1229.1"/>
<text text-anchor="middle" x="1481.68" y="-1446.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1481.68" y="-1430.1" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="1481.68" y="-1413.3" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1232.25,-1405.1 1731.12,-1405.1 "/>
<text text-anchor="start" x="1240.25" y="-1388.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1240.25" y="-1371.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revert(returndata: bytes)</text>
<text text-anchor="start" x="1240.25" y="-1354.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1240.25" y="-1338.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="1240.25" y="-1321.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1240.25" y="-1304.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="1240.25" y="-1287.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1240.25" y="-1270.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionDelegateCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1240.25" y="-1254.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResultFromTarget(target: address, success: bool, returndata: bytes): bytes</text>
<text text-anchor="start" x="1240.25" y="-1237.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes): bytes</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="1749.28,-1237.5 1749.28,-1455.1 2044.08,-1455.1 2044.08,-1237.5 1749.28,-1237.5"/>
<text text-anchor="middle" x="1896.68" y="-1438.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1896.68" y="-1421.7" font-family="Times,serif" font-size="14.00">StorageSlot</text>
<text text-anchor="middle" x="1896.68" y="-1404.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1749.28,-1396.7 2044.08,-1396.7 "/>
<text text-anchor="start" x="1757.28" y="-1380.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1757.28" y="-1363.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAddressSlot(slot: bytes32): (r: AddressSlot)</text>
<text text-anchor="start" x="1757.28" y="-1346.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBooleanSlot(slot: bytes32): (r: BooleanSlot)</text>
<text text-anchor="start" x="1757.28" y="-1329.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBytes32Slot(slot: bytes32): (r: Bytes32Slot)</text>
<text text-anchor="start" x="1757.28" y="-1312.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getUint256Slot(slot: bytes32): (r: Uint256Slot)</text>
<text text-anchor="start" x="1757.28" y="-1296.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getStringSlot(slot: bytes32): (r: StringSlot)</text>
<text text-anchor="start" x="1757.28" y="-1279.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getStringSlot(store: string): (r: StringSlot)</text>
<text text-anchor="start" x="1757.28" y="-1262.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBytesSlot(slot: bytes32): (r: BytesSlot)</text>
<text text-anchor="start" x="1757.28" y="-1245.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBytesSlot(store: bytes): (r: BytesSlot)</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="2613.27,-1546.5 2613.27,-1629.7 2878.09,-1629.7 2878.09,-1546.5 2613.27,-1546.5"/>
<text text-anchor="middle" x="2745.68" y="-1613.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2745.68" y="-1596.3" font-family="Times,serif" font-size="14.00">AddressSlot</text>
<text text-anchor="middle" x="2745.68" y="-1579.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2613.27,-1571.3 2878.09,-1571.3 "/>
<text text-anchor="start" x="2621.27" y="-1554.7" font-family="Times,serif" font-size="14.00">value: address</text>
</g>
<!-- 4&#45;&gt;5 -->
<g id="edge7" class="edge">
<title>4&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1976.54,-1455.2C1996.81,-1473.7 2019.92,-1490.51 2044.68,-1501.6 2156.47,-1551.66 2474.65,-1513.22 2594.68,-1537.6 2603.05,-1539.3 2611.59,-1541.37 2620.15,-1543.69"/>
<polygon fill="black" stroke="black" points="2619.24,-1547.07 2629.81,-1546.42 2621.14,-1540.34 2619.24,-1547.07"/>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="1198.27,-1546.5 1198.27,-1629.7 1463.09,-1629.7 1463.09,-1546.5 1198.27,-1546.5"/>
<text text-anchor="middle" x="1330.68" y="-1613.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1330.68" y="-1596.3" font-family="Times,serif" font-size="14.00">BooleanSlot</text>
<text text-anchor="middle" x="1330.68" y="-1579.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1198.27,-1571.3 1463.09,-1571.3 "/>
<text text-anchor="start" x="1206.27" y="-1554.7" font-family="Times,serif" font-size="14.00">value: bool</text>
</g>
<!-- 4&#45;&gt;6 -->
<g id="edge8" class="edge">
<title>4&#45;&gt;6</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1796.84,-1455.45C1776.69,-1473.45 1754.36,-1490 1730.68,-1501.6 1623.14,-1554.26 1580.14,-1509.75 1463.68,-1537.6 1456.27,-1539.37 1448.71,-1541.38 1441.13,-1543.56"/>
<polygon fill="black" stroke="black" points="1440.14,-1540.2 1431.54,-1546.39 1442.12,-1546.91 1440.14,-1540.2"/>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="1481.27,-1546.5 1481.27,-1629.7 1746.09,-1629.7 1746.09,-1546.5 1481.27,-1546.5"/>
<text text-anchor="middle" x="1613.68" y="-1613.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1613.68" y="-1596.3" font-family="Times,serif" font-size="14.00">Bytes32Slot</text>
<text text-anchor="middle" x="1613.68" y="-1579.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1481.27,-1571.3 1746.09,-1571.3 "/>
<text text-anchor="start" x="1489.27" y="-1554.7" font-family="Times,serif" font-size="14.00">value: bytes32</text>
</g>
<!-- 4&#45;&gt;7 -->
<g id="edge9" class="edge">
<title>4&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1782.95,-1455.4C1765.86,-1471.41 1748.12,-1487.31 1730.68,-1501.6 1714.25,-1515.06 1695.72,-1528.5 1678.31,-1540.6"/>
<polygon fill="black" stroke="black" points="1676.11,-1537.86 1669.86,-1546.42 1680.08,-1543.62 1676.11,-1537.86"/>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="1764.27,-1546.5 1764.27,-1629.7 2029.09,-1629.7 2029.09,-1546.5 1764.27,-1546.5"/>
<text text-anchor="middle" x="1896.68" y="-1613.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1896.68" y="-1596.3" font-family="Times,serif" font-size="14.00">Uint256Slot</text>
<text text-anchor="middle" x="1896.68" y="-1579.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1764.27,-1571.3 2029.09,-1571.3 "/>
<text text-anchor="start" x="1772.27" y="-1554.7" font-family="Times,serif" font-size="14.00">value: uint256</text>
</g>
<!-- 4&#45;&gt;8 -->
<g id="edge10" class="edge">
<title>4&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1889.96,-1455.27C1889.82,-1483.43 1890.18,-1512.47 1891.04,-1536.11"/>
<polygon fill="black" stroke="black" points="1887.55,-1536.37 1891.45,-1546.22 1894.54,-1536.09 1887.55,-1536.37"/>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="2047.27,-1546.5 2047.27,-1629.7 2312.09,-1629.7 2312.09,-1546.5 2047.27,-1546.5"/>
<text text-anchor="middle" x="2179.68" y="-1613.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2179.68" y="-1596.3" font-family="Times,serif" font-size="14.00">StringSlot</text>
<text text-anchor="middle" x="2179.68" y="-1579.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2047.27,-1571.3 2312.09,-1571.3 "/>
<text text-anchor="start" x="2055.27" y="-1554.7" font-family="Times,serif" font-size="14.00">value: string</text>
</g>
<!-- 4&#45;&gt;9 -->
<g id="edge11" class="edge">
<title>4&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1992.63,-1455.4C2009.5,-1471.41 2027.24,-1487.31 2044.68,-1501.6 2061.11,-1515.06 2079.64,-1528.5 2097.61,-1540.6"/>
<polygon fill="black" stroke="black" points="2096.11,-1543.8 2106.38,-1546.42 2099.99,-1537.97 2096.11,-1543.8"/>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="2330.27,-1546.5 2330.27,-1629.7 2595.09,-1629.7 2595.09,-1546.5 2330.27,-1546.5"/>
<text text-anchor="middle" x="2462.68" y="-1613.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2462.68" y="-1596.3" font-family="Times,serif" font-size="14.00">BytesSlot</text>
<text text-anchor="middle" x="2462.68" y="-1579.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2330.27,-1571.3 2595.09,-1571.3 "/>
<text text-anchor="start" x="2338.27" y="-1554.7" font-family="Times,serif" font-size="14.00">value: bytes</text>
</g>
<!-- 4&#45;&gt;10 -->
<g id="edge12" class="edge">
<title>4&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1978.88,-1455.45C1998.68,-1473.45 2021,-1490 2044.68,-1501.6 2152.22,-1554.26 2195.22,-1509.75 2311.68,-1537.6 2319.09,-1539.37 2326.66,-1541.38 2334.26,-1543.56"/>
<polygon fill="black" stroke="black" points="2333.32,-1546.93 2343.9,-1546.39 2335.29,-1540.21 2333.32,-1546.93"/>
</g>
<!-- 5&#45;&gt;4 -->
<g id="edge1" class="edge">
<title>5&#45;&gt;4</title>
<path fill="none" stroke="black" d="M2647.72,-1546.42C2636.05,-1542.98 2624.19,-1539.94 2612.68,-1537.6 2492.65,-1513.22 2174.47,-1551.66 2062.68,-1501.6 2041.5,-1492.11 2021.53,-1478.44 2003.27,-1463.11"/>
<polygon fill="black" stroke="black" points="2003.22,-1463.07 1996.07,-1462.16 1994.16,-1455.2 2001.31,-1456.11 2003.22,-1463.07"/>
</g>
<!-- 6&#45;&gt;4 -->
<g id="edge2" class="edge">
<title>6&#45;&gt;4</title>
<path fill="none" stroke="black" d="M1449.46,-1546.39C1460.29,-1543.12 1471.14,-1540.12 1481.68,-1537.6 1598.14,-1509.75 1641.14,-1554.26 1748.68,-1501.6 1768.66,-1491.81 1787.68,-1478.5 1805.02,-1463.77"/>
<polygon fill="black" stroke="black" points="1805.47,-1463.37 1807.34,-1456.41 1814.48,-1455.45 1812.62,-1462.42 1805.47,-1463.37"/>
</g>
<!-- 7&#45;&gt;4 -->
<g id="edge3" class="edge">
<title>7&#45;&gt;4</title>
<path fill="none" stroke="black" d="M1686.99,-1546.42C1707.6,-1532.89 1729.56,-1517.27 1748.68,-1501.6 1763.12,-1489.77 1777.77,-1476.83 1791.96,-1463.64"/>
<polygon fill="black" stroke="black" points="1791.98,-1463.62 1793.62,-1456.59 1800.73,-1455.4 1799.09,-1462.43 1791.98,-1463.62"/>
</g>
<!-- 8&#45;&gt;4 -->
<g id="edge4" class="edge">
<title>8&#45;&gt;4</title>
<path fill="none" stroke="black" d="M1901.91,-1546.22C1902.92,-1524.23 1903.43,-1495.84 1903.43,-1467.43"/>
<polygon fill="black" stroke="black" points="1903.43,-1467.27 1899.42,-1461.28 1903.4,-1455.27 1907.42,-1461.26 1903.43,-1467.27"/>
</g>
<!-- 9&#45;&gt;4 -->
<g id="edge5" class="edge">
<title>9&#45;&gt;4</title>
<path fill="none" stroke="black" d="M2123.5,-1546.42C2103.76,-1532.89 2081.8,-1517.27 2062.68,-1501.6 2048.24,-1489.77 2033.59,-1476.83 2019.28,-1463.64"/>
<polygon fill="black" stroke="black" points="2019.2,-1463.57 2012.09,-1462.42 2010.41,-1455.4 2017.53,-1456.56 2019.2,-1463.57"/>
</g>
<!-- 10&#45;&gt;4 -->
<g id="edge6" class="edge">
<title>10&#45;&gt;4</title>
<path fill="none" stroke="black" d="M2361.82,-1546.39C2351.07,-1543.12 2340.22,-1540.12 2329.68,-1537.6 2213.22,-1509.75 2170.22,-1554.26 2062.68,-1501.6 2042.51,-1491.72 2023.33,-1478.25 2005.64,-1463.36"/>
<polygon fill="black" stroke="black" points="2005.59,-1463.31 1998.44,-1462.4 1996.53,-1455.45 2003.68,-1456.36 2005.59,-1463.31"/>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="890.79,-1191.5 890.79,-1501.1 1204.57,-1501.1 1204.57,-1191.5 890.79,-1191.5"/>
<text text-anchor="middle" x="1047.68" y="-1484.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1047.68" y="-1467.7" font-family="Times,serif" font-size="14.00">Initializable</text>
<text text-anchor="middle" x="1047.68" y="-1450.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="890.79,-1442.7 1204.57,-1442.7 "/>
<text text-anchor="start" x="898.79" y="-1426.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="898.79" y="-1409.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;INITIALIZABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="890.79,-1401.1 1204.57,-1401.1 "/>
<text text-anchor="start" x="898.79" y="-1384.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="898.79" y="-1367.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializableStorage(): ($: InitializableStorage)</text>
<text text-anchor="start" x="898.79" y="-1350.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="898.79" y="-1334.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkInitializing()</text>
<text text-anchor="start" x="898.79" y="-1317.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_disableInitializers()</text>
<text text-anchor="start" x="898.79" y="-1300.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializedVersion(): uint64</text>
<text text-anchor="start" x="898.79" y="-1283.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isInitializing(): bool</text>
<text text-anchor="start" x="898.79" y="-1266.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="898.79" y="-1250.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Initialized(version: uint64)</text>
<text text-anchor="start" x="898.79" y="-1233.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; initializer()</text>
<text text-anchor="start" x="898.79" y="-1216.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; reinitializer(version: uint64)</text>
<text text-anchor="start" x="898.79" y="-1199.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyInitializing()</text>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="915.27,-1538.1 915.27,-1638.1 1180.09,-1638.1 1180.09,-1538.1 915.27,-1538.1"/>
<text text-anchor="middle" x="1047.68" y="-1621.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1047.68" y="-1604.7" font-family="Times,serif" font-size="14.00">InitializableStorage</text>
<text text-anchor="middle" x="1047.68" y="-1587.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="915.27,-1579.7 1180.09,-1579.7 "/>
<text text-anchor="start" x="923.27" y="-1563.1" font-family="Times,serif" font-size="14.00">_initialized: uint64</text>
<text text-anchor="start" x="923.27" y="-1546.3" font-family="Times,serif" font-size="14.00">_initializing: bool</text>
</g>
<!-- 11&#45;&gt;12 -->
<g id="edge14" class="edge">
<title>11&#45;&gt;12</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1041.18,-1501.2C1041.31,-1510.31 1041.51,-1519.13 1041.75,-1527.43"/>
<polygon fill="black" stroke="black" points="1038.26,-1527.78 1042.1,-1537.65 1045.26,-1527.54 1038.26,-1527.78"/>
</g>
<!-- 12&#45;&gt;11 -->
<g id="edge13" class="edge">
<title>12&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1053.26,-1537.65C1053.55,-1530.09 1053.78,-1521.95 1053.96,-1513.45"/>
<polygon fill="black" stroke="black" points="1053.97,-1513.2 1050.08,-1507.13 1054.18,-1501.2 1058.08,-1507.28 1053.97,-1513.2"/>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#f2f2f2" stroke="black" points="3024.63,-899.3 3024.63,-999.3 3554.73,-999.3 3554.73,-899.3 3024.63,-899.3"/>
<text text-anchor="middle" x="3289.68" y="-982.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3289.68" y="-965.9" font-family="Times,serif" font-size="14.00">IPythEvents</text>
<text text-anchor="middle" x="3289.68" y="-949.1" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="3024.63,-940.9 3554.73,-940.9 "/>
<text text-anchor="start" x="3032.63" y="-924.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3032.63" y="-907.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PriceFeedUpdate(id: bytes32, publishTime: uint64, price: int64, conf: uint64)</text>
</g>
<!-- 14 -->
<g id="node15" class="node">
<title>14</title>
<polygon fill="#f2f2f2" stroke="black" points="2576.27,-1325.5 2576.27,-1367.1 2841.09,-1367.1 2841.09,-1325.5 2576.27,-1325.5"/>
<text text-anchor="middle" x="2708.68" y="-1350.5" font-family="Times,serif" font-size="14.00">PythStructs</text>
<text text-anchor="middle" x="2708.68" y="-1333.7" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
</g>
<!-- 15 -->
<g id="node16" class="node">
<title>15</title>
<polygon fill="#f2f2f2" stroke="black" points="2656.27,-882.5 2656.27,-1016.1 2921.09,-1016.1 2921.09,-882.5 2656.27,-882.5"/>
<text text-anchor="middle" x="2788.68" y="-999.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2788.68" y="-982.7" font-family="Times,serif" font-size="14.00">Price</text>
<text text-anchor="middle" x="2788.68" y="-965.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2656.27,-957.7 2921.09,-957.7 "/>
<text text-anchor="start" x="2664.27" y="-941.1" font-family="Times,serif" font-size="14.00">price: int64</text>
<text text-anchor="start" x="2664.27" y="-924.3" font-family="Times,serif" font-size="14.00">conf: uint64</text>
<text text-anchor="start" x="2664.27" y="-907.5" font-family="Times,serif" font-size="14.00">expo: int32</text>
<text text-anchor="start" x="2664.27" y="-890.7" font-family="Times,serif" font-size="14.00">publishTime: uint</text>
</g>
<!-- 15&#45;&gt;14 -->
<g id="edge15" class="edge">
<title>15&#45;&gt;14</title>
<path fill="none" stroke="black" d="M2775.29,-1016.41C2757.9,-1102.3 2728.22,-1248.85 2715.16,-1313.29"/>
<polygon fill="black" stroke="black" points="2715.09,-1313.64 2717.82,-1320.31 2712.71,-1325.4 2709.98,-1318.72 2715.09,-1313.64"/>
</g>
<!-- 16 -->
<g id="node17" class="node">
<title>16</title>
<polygon fill="#f2f2f2" stroke="black" points="2496.27,-460.3 2496.27,-577.1 2761.09,-577.1 2761.09,-460.3 2496.27,-460.3"/>
<text text-anchor="middle" x="2628.68" y="-560.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2628.68" y="-543.7" font-family="Times,serif" font-size="14.00">PriceFeed</text>
<text text-anchor="middle" x="2628.68" y="-526.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2496.27,-518.7 2761.09,-518.7 "/>
<text text-anchor="start" x="2504.27" y="-502.1" font-family="Times,serif" font-size="14.00">id: bytes32</text>
<text text-anchor="start" x="2504.27" y="-485.3" font-family="Times,serif" font-size="14.00">price: Price</text>
<text text-anchor="start" x="2504.27" y="-468.5" font-family="Times,serif" font-size="14.00">emaPrice: Price</text>
</g>
<!-- 16&#45;&gt;14 -->
<g id="edge16" class="edge">
<title>16&#45;&gt;14</title>
<path fill="none" stroke="black" d="M2625.06,-577.18C2619.18,-689.29 2611.55,-944.56 2647.68,-1155 2657.57,-1212.6 2680.86,-1276.8 2695.63,-1313.97"/>
<polygon fill="black" stroke="black" points="2695.79,-1314.36 2701.74,-1318.43 2700.28,-1325.49 2694.33,-1321.42 2695.79,-1314.36"/>
</g>
<!-- 16&#45;&gt;15 -->
<g id="edge17" class="edge">
<title>16&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2650.26,-577.5C2678.76,-653.85 2729.01,-788.46 2760.49,-872.78"/>
<polygon fill="black" stroke="black" points="2757.26,-874.15 2764.04,-882.3 2763.82,-871.7 2757.26,-874.15"/>
</g>
<!-- 17 -->
<g id="node18" class="node">
<title>17</title>
<polygon fill="#f2f2f2" stroke="black" points="567.27,-899.3 567.27,-999.3 832.09,-999.3 832.09,-899.3 567.27,-899.3"/>
<text text-anchor="middle" x="699.68" y="-982.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="699.68" y="-965.9" font-family="Times,serif" font-size="14.00">IOracleProvider</text>
<text text-anchor="middle" x="699.68" y="-949.1" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="567.27,-940.9 832.09,-940.9 "/>
<text text-anchor="start" x="575.27" y="-924.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="575.27" y="-907.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPrice(asset: address): uint256</text>
</g>
<!-- 18 -->
<g id="node19" class="node">
<title>18</title>
<polygon fill="#f2f2f2" stroke="black" points="82.27,-451.9 82.27,-585.5 347.09,-585.5 347.09,-451.9 82.27,-451.9"/>
<text text-anchor="middle" x="214.68" y="-568.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="214.68" y="-552.1" font-family="Times,serif" font-size="14.00">IERC20Metadata</text>
<text text-anchor="middle" x="214.68" y="-535.3" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="82.27,-527.1 347.09,-527.1 "/>
<text text-anchor="start" x="90.27" y="-510.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="90.27" y="-493.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="90.27" y="-476.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="90.27" y="-460.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
</g>
<!-- 18&#45;&gt;2 -->
<g id="edge18" class="edge">
<title>18&#45;&gt;2</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M214.68,-585.73C214.68,-643.18 214.68,-728.52 214.68,-801.98"/>
<polygon fill="none" stroke="black" points="204.18,-802.08 214.68,-832.08 225.18,-802.08 204.18,-802.08"/>
</g>
<!-- 19 -->
<g id="node20" class="node">
<title>19</title>
<polygon fill="#f2f2f2" stroke="black" points="893.05,-865.7 893.05,-1032.9 1202.31,-1032.9 1202.31,-865.7 893.05,-865.7"/>
<text text-anchor="middle" x="1047.68" y="-1016.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1047.68" y="-999.5" font-family="Times,serif" font-size="14.00">ContextUpgradeable</text>
<text text-anchor="middle" x="1047.68" y="-982.7" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="893.05,-974.5 1202.31,-974.5 "/>
<text text-anchor="start" x="901.05" y="-957.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="901.05" y="-941.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Context_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="901.05" y="-924.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Context_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="901.05" y="-907.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="901.05" y="-890.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="901.05" y="-873.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 19&#45;&gt;11 -->
<g id="edge19" class="edge">
<title>19&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1047.68,-1033.1C1047.68,-1070.5 1047.68,-1116.3 1047.68,-1161.17"/>
<polygon fill="none" stroke="black" points="1037.18,-1161.2 1047.68,-1191.2 1058.18,-1161.2 1037.18,-1161.2"/>
</g>
<!-- 20 -->
<g id="node21" class="node">
<title>20</title>
<polygon fill="#f2f2f2" stroke="black" points="365.03,-451.9 365.03,-585.5 1034.33,-585.5 1034.33,-451.9 365.03,-451.9"/>
<text text-anchor="middle" x="699.68" y="-568.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="699.68" y="-552.1" font-family="Times,serif" font-size="14.00">IPythOracleProvider</text>
<text text-anchor="middle" x="699.68" y="-535.3" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="365.03,-527.1 1034.33,-527.1 "/>
<text text-anchor="start" x="373.03" y="-510.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="373.03" y="-493.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setAssetConfig(asset: address, feed: bytes32, confidenceRatioMin: uint256, maxStalePeriod: uint256)</text>
<text text-anchor="start" x="373.03" y="-476.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="373.03" y="-460.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AssetConfigSet(asset: address, feed: bytes32, confidenceRatioMin: uint256, maxStalePeriod: uint256)</text>
</g>
<!-- 20&#45;&gt;17 -->
<g id="edge20" class="edge">
<title>20&#45;&gt;17</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M699.68,-585.73C699.68,-662.03 699.68,-787.53 699.68,-869.04"/>
<polygon fill="none" stroke="black" points="689.18,-869.21 699.68,-899.21 710.18,-869.21 689.18,-869.21"/>
</g>
<!-- 21 -->
<g id="node22" class="node">
<title>21</title>
<polygon fill="#f2f2f2" stroke="black" points="1052.22,-355.5 1052.22,-681.9 1537.15,-681.9 1537.15,-355.5 1052.22,-355.5"/>
<text text-anchor="middle" x="1294.68" y="-665.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1294.68" y="-648.5" font-family="Times,serif" font-size="14.00">OwnableUpgradeable</text>
<text text-anchor="middle" x="1294.68" y="-631.7" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1052.22,-623.5 1537.15,-623.5 "/>
<text text-anchor="start" x="1060.22" y="-606.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1060.22" y="-590.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;OwnableStorageLocation: bytes32</text>
<polyline fill="none" stroke="black" points="1052.22,-581.9 1537.15,-581.9 "/>
<text text-anchor="start" x="1060.22" y="-565.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1060.22" y="-548.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: OwnableStorage)</text>
<text text-anchor="start" x="1060.22" y="-531.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1060.22" y="-514.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Ownable_init(initialOwner: address) &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1060.22" y="-498.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Ownable_init_unchained(initialOwner: address) &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1060.22" y="-481.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="1060.22" y="-464.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transferOwnership(newOwner: address)</text>
<text text-anchor="start" x="1060.22" y="-447.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1060.22" y="-430.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnershipTransferred(previousOwner: address, newOwner: address)</text>
<text text-anchor="start" x="1060.22" y="-414.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
<text text-anchor="start" x="1060.22" y="-397.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="1060.22" y="-380.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceOwnership() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="1060.22" y="-363.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferOwnership(newOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
</g>
<!-- 21&#45;&gt;11 -->
<g id="edge22" class="edge">
<title>21&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1092.25,-681.92C1076.27,-691.38 1059.99,-700.08 1043.68,-707.6 977.49,-738.12 929.21,-686.68 883.68,-743.6 826.57,-814.99 851.85,-1069.3 883.68,-1155 884.88,-1158.21 886.15,-1161.41 887.5,-1164.59"/>
<polygon fill="none" stroke="black" points="878.12,-1169.31 900.89,-1191.49 896.92,-1159.95 878.12,-1169.31"/>
</g>
<!-- 21&#45;&gt;19 -->
<g id="edge23" class="edge">
<title>21&#45;&gt;19</title>
<path fill="none" stroke="black" d="M1201.12,-682.04C1170.85,-734.57 1138.02,-791.54 1110.53,-839.25"/>
<polygon fill="none" stroke="black" points="1101.32,-834.2 1095.44,-865.44 1119.51,-844.69 1101.32,-834.2"/>
</g>
<!-- 22 -->
<g id="node23" class="node">
<title>22</title>
<polygon fill="#f2f2f2" stroke="black" points="1220.27,-907.7 1220.27,-990.9 1485.09,-990.9 1485.09,-907.7 1220.27,-907.7"/>
<text text-anchor="middle" x="1352.68" y="-974.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1352.68" y="-957.5" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="1352.68" y="-940.7" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1220.27,-932.5 1485.09,-932.5 "/>
<text text-anchor="start" x="1228.27" y="-915.9" font-family="Times,serif" font-size="14.00">_owner: address</text>
</g>
<!-- 21&#45;&gt;22 -->
<g id="edge24" class="edge">
<title>21&#45;&gt;22</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1310.08,-682.04C1319.88,-758.45 1332.16,-844.24 1341.22,-897.28"/>
<polygon fill="black" stroke="black" points="1337.82,-898.18 1342.98,-907.44 1344.72,-896.99 1337.82,-898.18"/>
</g>
<!-- 22&#45;&gt;21 -->
<g id="edge21" class="edge">
<title>22&#45;&gt;21</title>
<path fill="none" stroke="black" d="M1351.32,-907.44C1346.73,-858.25 1335.95,-772.45 1324.92,-694.05"/>
<polygon fill="black" stroke="black" points="1324.9,-693.92 1320.1,-688.54 1323.22,-682.04 1328.02,-687.42 1324.9,-693.92"/>
</g>
<!-- 23 -->
<g id="node24" class="node">
<title>23</title>
<polygon fill="#f2f2f2" stroke="black" points="2817.06,-401.5 2817.06,-635.9 3762.31,-635.9 3762.31,-401.5 2817.06,-401.5"/>
<text text-anchor="middle" x="3289.68" y="-619.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3289.68" y="-602.5" font-family="Times,serif" font-size="14.00">IPyth</text>
<text text-anchor="middle" x="3289.68" y="-585.7" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2817.06,-577.5 3762.31,-577.5 "/>
<text text-anchor="start" x="2825.06" y="-560.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2825.06" y="-544.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPriceUnsafe(id: bytes32): (price: PythStructs.Price)</text>
<text text-anchor="start" x="2825.06" y="-527.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPriceNoOlderThan(id: bytes32, age: uint): (price: PythStructs.Price)</text>
<text text-anchor="start" x="2825.06" y="-510.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getEmaPriceUnsafe(id: bytes32): (price: PythStructs.Price)</text>
<text text-anchor="start" x="2825.06" y="-493.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getEmaPriceNoOlderThan(id: bytes32, age: uint): (price: PythStructs.Price)</text>
<text text-anchor="start" x="2825.06" y="-476.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;updatePriceFeeds(updateData: bytes[])</text>
<text text-anchor="start" x="2825.06" y="-460.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;updatePriceFeedsIfNecessary(updateData: bytes[], priceIds: bytes32[], publishTimes: uint64[])</text>
<text text-anchor="start" x="2825.06" y="-443.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getUpdateFee(updateData: bytes[]): (feeAmount: uint)</text>
<text text-anchor="start" x="2825.06" y="-426.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;parsePriceFeedUpdates(updateData: bytes[], priceIds: bytes32[], minPublishTime: uint64, maxPublishTime: uint64): (priceFeeds: PythStructs.PriceFeed[])</text>
<text text-anchor="start" x="2825.06" y="-409.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;parsePriceFeedUpdatesUnique(updateData: bytes[], priceIds: bytes32[], minPublishTime: uint64, maxPublishTime: uint64): (priceFeeds: PythStructs.PriceFeed[])</text>
</g>
<!-- 23&#45;&gt;13 -->
<g id="edge25" class="edge">
<title>23&#45;&gt;13</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3289.68,-635.97C3289.68,-709.94 3289.68,-803.77 3289.68,-869.19"/>
<polygon fill="none" stroke="black" points="3279.18,-869.25 3289.68,-899.25 3300.18,-869.25 3279.18,-869.25"/>
</g>
<!-- 23&#45;&gt;14 -->
<g id="edge26" class="edge">
<title>23&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3478.7,-636.01C3513.09,-666.49 3544.2,-702.53 3563.68,-743.6 3642.03,-908.81 3678.07,-1012.35 3563.68,-1155 3475.96,-1264.39 3070.93,-1314.93 2851.28,-1334.64"/>
<polygon fill="black" stroke="black" points="2850.93,-1331.16 2841.28,-1335.53 2851.55,-1338.13 2850.93,-1331.16"/>
</g>
<!-- 23&#45;&gt;15 -->
<g id="edge27" class="edge">
<title>23&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3144.61,-636.05C3102.66,-670.28 3057.05,-708.08 3015.68,-743.6 2965.63,-786.57 2910.66,-836.08 2867.36,-875.64"/>
<polygon fill="black" stroke="black" points="2864.98,-873.07 2859.96,-882.4 2869.7,-878.24 2864.98,-873.07"/>
</g>
<!-- 24 -->
<g id="node25" class="node">
<title>24</title>
<polygon fill="#f2f2f2" stroke="black" points="1540.46,-744.1 1540.46,-1154.5 1988.91,-1154.5 1988.91,-744.1 1540.46,-744.1"/>
<text text-anchor="middle" x="1764.68" y="-1137.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1764.68" y="-1121.1" font-family="Times,serif" font-size="14.00">ERC1967Utils</text>
<text text-anchor="middle" x="1764.68" y="-1104.3" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1540.46,-1096.1 1988.91,-1096.1 "/>
<text text-anchor="start" x="1548.46" y="-1079.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1548.46" y="-1062.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;IMPLEMENTATION_SLOT: bytes32</text>
<text text-anchor="start" x="1548.46" y="-1045.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;ADMIN_SLOT: bytes32</text>
<text text-anchor="start" x="1548.46" y="-1029.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;BEACON_SLOT: bytes32</text>
<polyline fill="none" stroke="black" points="1540.46,-1020.9 1988.91,-1020.9 "/>
<text text-anchor="start" x="1548.46" y="-1004.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1548.46" y="-987.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setImplementation(newImplementation: address)</text>
<text text-anchor="start" x="1548.46" y="-970.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setAdmin(newAdmin: address)</text>
<text text-anchor="start" x="1548.46" y="-953.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setBeacon(newBeacon: address)</text>
<text text-anchor="start" x="1548.46" y="-937.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNonPayable()</text>
<text text-anchor="start" x="1548.46" y="-920.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1548.46" y="-903.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getImplementation(): address</text>
<text text-anchor="start" x="1548.46" y="-886.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;upgradeToAndCall(newImplementation: address, data: bytes)</text>
<text text-anchor="start" x="1548.46" y="-869.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAdmin(): address</text>
<text text-anchor="start" x="1548.46" y="-853.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;changeAdmin(newAdmin: address)</text>
<text text-anchor="start" x="1548.46" y="-836.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getBeacon(): address</text>
<text text-anchor="start" x="1548.46" y="-819.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;upgradeBeaconToAndCall(newBeacon: address, data: bytes)</text>
<text text-anchor="start" x="1548.46" y="-802.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1548.46" y="-785.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Upgraded(implementation: address)</text>
<text text-anchor="start" x="1548.46" y="-769.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AdminChanged(previousAdmin: address, newAdmin: address)</text>
<text text-anchor="start" x="1548.46" y="-752.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; BeaconUpgraded(beacon: address)</text>
</g>
<!-- 24&#45;&gt;1 -->
<g id="edge30" class="edge">
<title>24&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1989,-1147.53C1992.24,-1150.05 1995.47,-1152.54 1998.68,-1155 2021.59,-1172.53 2030.76,-1172.25 2052.68,-1191 2087.24,-1220.55 2121.89,-1258 2148.3,-1288.61"/>
<polygon fill="black" stroke="black" points="2145.67,-1290.92 2154.83,-1296.23 2150.99,-1286.36 2145.67,-1290.92"/>
</g>
<!-- 24&#45;&gt;3 -->
<g id="edge29" class="edge">
<title>24&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1618.21,-1154.74C1602.07,-1177.26 1586.03,-1199.66 1570.91,-1220.76"/>
<polygon fill="black" stroke="black" points="1567.92,-1218.93 1564.94,-1229.1 1573.61,-1223.01 1567.92,-1218.93"/>
</g>
<!-- 24&#45;&gt;4 -->
<g id="edge28" class="edge">
<title>24&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1833,-1154.74C1841.34,-1179.7 1849.63,-1204.5 1857.34,-1227.57"/>
<polygon fill="black" stroke="black" points="1854.11,-1228.96 1860.6,-1237.34 1860.75,-1226.74 1854.11,-1228.96"/>
</g>
<!-- 25 -->
<g id="node26" class="node">
<title>25</title>
<polygon fill="#f2f2f2" stroke="black" points="1554.66,-330.3 1554.66,-707.1 2102.7,-707.1 2102.7,-330.3 1554.66,-330.3"/>
<text text-anchor="middle" x="1828.68" y="-690.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1828.68" y="-673.7" font-family="Times,serif" font-size="14.00">UUPSUpgradeable</text>
<text text-anchor="middle" x="1828.68" y="-656.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1554.66,-648.7 2102.7,-648.7 "/>
<text text-anchor="start" x="1562.66" y="-632.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1562.66" y="-615.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;__self: address</text>
<text text-anchor="start" x="1562.66" y="-598.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1562.66" y="-581.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;UPGRADE_INTERFACE_VERSION: string</text>
<polyline fill="none" stroke="black" points="1554.66,-573.5 2102.7,-573.5 "/>
<text text-anchor="start" x="1562.66" y="-556.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1562.66" y="-540.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_upgradeToAndCallUUPS(newImplementation: address, data: bytes)</text>
<text text-anchor="start" x="1562.66" y="-523.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1562.66" y="-506.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;abstract&gt;&gt; _authorizeUpgrade(newImplementation: address)</text>
<text text-anchor="start" x="1562.66" y="-489.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__UUPSUpgradeable_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1562.66" y="-472.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__UUPSUpgradeable_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1562.66" y="-456.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkProxy()</text>
<text text-anchor="start" x="1562.66" y="-439.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNotDelegated()</text>
<text text-anchor="start" x="1562.66" y="-422.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1562.66" y="-405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;proxiableUUID(): bytes32 &lt;&lt;notDelegated&gt;&gt;</text>
<text text-anchor="start" x="1562.66" y="-388.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1562.66" y="-372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;payable&gt;&gt; upgradeToAndCall(newImplementation: address, data: bytes) &lt;&lt;onlyProxy&gt;&gt;</text>
<text text-anchor="start" x="1562.66" y="-355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyProxy()</text>
<text text-anchor="start" x="1562.66" y="-338.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; notDelegated()</text>
</g>
<!-- 25&#45;&gt;0 -->
<g id="edge32" class="edge">
<title>25&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1971.01,-707.12C1980.09,-719.43 1989.04,-731.67 1997.68,-743.6 2028.74,-786.51 2062.58,-835.21 2089.43,-874.34"/>
<polygon fill="none" stroke="black" points="2080.77,-880.27 2106.37,-899.11 2098.1,-868.42 2080.77,-880.27"/>
</g>
<!-- 25&#45;&gt;11 -->
<g id="edge31" class="edge">
<title>25&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1555.52,-707.43C1546.7,-718.99 1538.68,-731.05 1531.68,-743.6 1486.98,-823.8 1557.28,-1088.78 1493.68,-1155 1456.5,-1193.71 1316.3,-1172.4 1233.38,-1191.19"/>
<polygon fill="none" stroke="black" points="1229.98,-1181.26 1204.74,-1200.57 1236.51,-1201.22 1229.98,-1181.26"/>
</g>
<!-- 25&#45;&gt;24 -->
<g id="edge33" class="edge">
<title>25&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1800.66,-707.38C1799.34,-716.21 1798.01,-725.1 1796.68,-733.99"/>
<polygon fill="black" stroke="black" points="1793.2,-733.62 1795.18,-744.03 1800.12,-734.66 1793.2,-733.62"/>
</g>
<!-- 26 -->
<g id="node27" class="node">
<title>26</title>
<polygon fill="#f2f2f2" stroke="black" points="1478.87,-0.5 1478.87,-293.3 2178.5,-293.3 2178.5,-0.5 1478.87,-0.5"/>
<text text-anchor="middle" x="1828.68" y="-276.7" font-family="Times,serif" font-size="14.00">PythOracleProvider</text>
<text text-anchor="middle" x="1828.68" y="-259.9" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="1478.87,-251.7 2178.5,-251.7 "/>
<text text-anchor="start" x="1486.87" y="-235.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1486.87" y="-218.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_ORACLE_PROVIDER_STORAGE: bytes32</text>
<text text-anchor="start" x="1486.87" y="-201.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1486.87" y="-184.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;pyth: IPyth</text>
<text text-anchor="start" x="1486.87" y="-167.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;initialOwner: address</text>
<polyline fill="none" stroke="black" points="1478.87,-159.7 2178.5,-159.7 "/>
<text text-anchor="start" x="1486.87" y="-143.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1486.87" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_authorizeUpgrade(newImplementation: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="1486.87" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getProviderStorage(): (data: OracleProviderStorage)</text>
<text text-anchor="start" x="1486.87" y="-92.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1486.87" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setAssetConfig(asset: address, feed: bytes32, confidenceRatioMin: uint256, maxStalePeriod: uint256) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="1486.87" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getPrice(asset: address): uint256</text>
<text text-anchor="start" x="1486.87" y="-42.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1486.87" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(_pyth: address, _initialOwner: address)</text>
<text text-anchor="start" x="1486.87" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;initialize() &lt;&lt;initializer&gt;&gt;</text>
</g>
<!-- 26&#45;&gt;14 -->
<g id="edge41" class="edge">
<title>26&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2084.59,-293.53C2094.68,-304.89 2103.82,-316.98 2111.68,-329.8 2155.78,-401.74 2095.23,-643.14 2149.68,-707.6 2188.65,-753.73 2241.91,-697.32 2280.68,-743.6 2339.64,-813.98 2266.5,-1079.46 2318.68,-1155 2383.44,-1248.75 2507.8,-1298.15 2598.74,-1322.88"/>
<polygon fill="black" stroke="black" points="2597.98,-1326.3 2608.54,-1325.48 2599.77,-1319.53 2597.98,-1326.3"/>
</g>
<!-- 26&#45;&gt;15 -->
<g id="edge42" class="edge">
<title>26&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2178.6,-170.66C2413.43,-193.96 2693.3,-240.08 2769.68,-329.8 2804.05,-370.17 2796.71,-716.09 2791.55,-872.29"/>
<polygon fill="black" stroke="black" points="2788.05,-872.19 2791.22,-882.3 2795.05,-872.43 2788.05,-872.19"/>
</g>
<!-- 26&#45;&gt;18 -->
<g id="edge43" class="edge">
<title>26&#45;&gt;18</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1478.72,-159.82C1104.74,-177.99 540.68,-222.66 355.68,-329.8 310.64,-355.89 275.46,-402.67 251.51,-443.03"/>
<polygon fill="black" stroke="black" points="248.41,-441.39 246.41,-451.8 254.46,-444.9 248.41,-441.39"/>
</g>
<!-- 26&#45;&gt;20 -->
<g id="edge36" class="edge">
<title>26&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1478.72,-207.72C1340.67,-236.78 1182.26,-277.03 1043.68,-329.8 969.53,-358.03 891.57,-399.53 828.65,-436.34"/>
<polygon fill="none" stroke="black" points="823.08,-427.43 802.66,-451.78 833.81,-445.49 823.08,-427.43"/>
</g>
<!-- 26&#45;&gt;21 -->
<g id="edge38" class="edge">
<title>26&#45;&gt;21</title>
<path fill="none" stroke="black" d="M1599.16,-293.45C1580.96,-305.59 1562.95,-317.81 1545.68,-329.8 1541.89,-332.44 1538.07,-335.1 1534.23,-337.8"/>
<polygon fill="none" stroke="black" points="1528,-329.34 1509.6,-355.26 1540.15,-346.47 1528,-329.34"/>
</g>
<!-- 26&#45;&gt;23 -->
<g id="edge39" class="edge">
<title>26&#45;&gt;23</title>
<path fill="none" stroke="black" d="M2178.52,-197.28C2365.62,-227.67 2598.82,-271.97 2802.68,-329.8 2869.16,-348.66 2939.24,-372.95 3004.88,-397.77"/>
<polygon fill="black" stroke="black" points="3003.94,-401.16 3014.53,-401.44 3006.43,-394.62 3003.94,-401.16"/>
</g>
<!-- 26&#45;&gt;25 -->
<g id="edge37" class="edge">
<title>26&#45;&gt;25</title>
<path fill="none" stroke="black" d="M1828.68,-293.52C1828.68,-295.68 1828.68,-297.85 1828.68,-300.03"/>
<polygon fill="none" stroke="black" points="1818.18,-300.08 1828.68,-330.08 1839.18,-300.08 1818.18,-300.08"/>
</g>
<!-- 27 -->
<g id="node28" class="node">
<title>27</title>
<polygon fill="#f2f2f2" stroke="black" points="2158.27,-477.1 2158.27,-560.3 2423.09,-560.3 2423.09,-477.1 2158.27,-477.1"/>
<text text-anchor="middle" x="2290.68" y="-543.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2290.68" y="-526.9" font-family="Times,serif" font-size="14.00">OracleProviderStorage</text>
<text text-anchor="middle" x="2290.68" y="-510.1" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2158.27,-501.9 2423.09,-501.9 "/>
<text text-anchor="start" x="2166.27" y="-485.3" font-family="Times,serif" font-size="14.00">configs: mapping(address=&gt;AssetConfig)</text>
</g>
<!-- 26&#45;&gt;27 -->
<g id="edge44" class="edge">
<title>26&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2089.81,-293.45C2105.8,-305.09 2121.24,-317.23 2135.68,-329.8 2182.37,-370.44 2224.42,-427.88 2253.08,-468.53"/>
<polygon fill="black" stroke="black" points="2250.37,-470.77 2258.98,-476.94 2256.1,-466.74 2250.37,-470.77"/>
</g>
<!-- 28 -->
<g id="node29" class="node">
<title>28</title>
<polygon fill="#f2f2f2" stroke="black" points="2327.27,-890.9 2327.27,-1007.7 2592.09,-1007.7 2592.09,-890.9 2327.27,-890.9"/>
<text text-anchor="middle" x="2459.68" y="-991.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2459.68" y="-974.3" font-family="Times,serif" font-size="14.00">AssetConfig</text>
<text text-anchor="middle" x="2459.68" y="-957.5" font-family="Times,serif" font-size="14.00">public/flatten/PythOracleProvider.flatten.sol</text>
<polyline fill="none" stroke="black" points="2327.27,-949.3 2592.09,-949.3 "/>
<text text-anchor="start" x="2335.27" y="-932.7" font-family="Times,serif" font-size="14.00">feed: bytes32</text>
<text text-anchor="start" x="2335.27" y="-915.9" font-family="Times,serif" font-size="14.00">confidenceRatioMin: uint256</text>
<text text-anchor="start" x="2335.27" y="-899.1" font-family="Times,serif" font-size="14.00">maxStalePeriod: uint256</text>
</g>
<!-- 26&#45;&gt;28 -->
<g id="edge40" class="edge">
<title>26&#45;&gt;28</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2178.81,-219.39C2287.99,-248.82 2388.8,-286.28 2422.68,-329.8 2488.07,-413.78 2469.04,-737.98 2461.25,-880.47"/>
<polygon fill="black" stroke="black" points="2457.73,-880.64 2460.68,-890.82 2464.72,-881.02 2457.73,-880.64"/>
</g>
<!-- 27&#45;&gt;26 -->
<g id="edge34" class="edge">
<title>27&#45;&gt;26</title>
<path fill="none" stroke="black" d="M2272.55,-476.94C2248.38,-436.14 2203.69,-373.33 2153.68,-329.8 2142.17,-319.78 2130.03,-310.04 2117.46,-300.6"/>
<polygon fill="black" stroke="black" points="2117.43,-300.58 2110.23,-300.23 2107.78,-293.45 2114.98,-293.8 2117.43,-300.58"/>
</g>
<!-- 27&#45;&gt;28 -->
<g id="edge45" class="edge">
<title>27&#45;&gt;28</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2306.73,-560.41C2335.87,-634.3 2397.72,-791.15 2433.19,-881.12"/>
<polygon fill="black" stroke="black" points="2430.04,-882.66 2436.96,-890.68 2436.55,-880.1 2430.04,-882.66"/>
</g>
<!-- 28&#45;&gt;26 -->
<g id="edge35" class="edge">
<title>28&#45;&gt;26</title>
<path fill="none" stroke="black" d="M2469.71,-890.82C2486.04,-754.36 2507.7,-415.89 2440.68,-329.8 2406.3,-285.63 2302.98,-247.7 2190.78,-218.08"/>
<polygon fill="black" stroke="black" points="2190.45,-218 2183.63,-220.35 2178.84,-214.97 2185.65,-212.61 2190.45,-218"/>
</g>
</g>
</svg>
