<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="4583pt" height="3800pt"
 viewBox="0.00 0.00 4582.83 3800.40" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 3796.4)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-3796.4 4578.83,-3796.4 4578.83,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="617.99,-1298.5 617.99,-1532.9 1228.6,-1532.9 1228.6,-1298.5 617.99,-1298.5"/>
<text text-anchor="middle" x="923.29" y="-1516.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="923.29" y="-1499.5" font-family="Times,serif" font-size="14.00">IAccessControl</text>
<text text-anchor="middle" x="923.29" y="-1482.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="617.99,-1474.5 1228.6,-1474.5 "/>
<text text-anchor="start" x="625.99" y="-1457.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="625.99" y="-1441.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasRole(role: bytes32, account: address): bool</text>
<text text-anchor="start" x="625.99" y="-1424.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getRoleAdmin(role: bytes32): bytes32</text>
<text text-anchor="start" x="625.99" y="-1407.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantRole(role: bytes32, account: address)</text>
<text text-anchor="start" x="625.99" y="-1390.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokeRole(role: bytes32, account: address)</text>
<text text-anchor="start" x="625.99" y="-1373.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceRole(role: bytes32, callerConfirmation: address)</text>
<text text-anchor="start" x="625.99" y="-1357.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="625.99" y="-1340.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RoleAdminChanged(role: bytes32, previousAdminRole: bytes32, newAdminRole: bytes32)</text>
<text text-anchor="start" x="625.99" y="-1323.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RoleGranted(role: bytes32, account: address, sender: address)</text>
<text text-anchor="start" x="625.99" y="-1306.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RoleRevoked(role: bytes32, account: address, sender: address)</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="2707,-3357.3 2707,-3415.7 2937.59,-3415.7 2937.59,-3357.3 2707,-3357.3"/>
<text text-anchor="middle" x="2822.29" y="-3399.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2822.29" y="-3382.3" font-family="Times,serif" font-size="14.00">IERC20Errors</text>
<text text-anchor="middle" x="2822.29" y="-3365.5" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="2734,-109.3 2734,-167.7 2964.59,-167.7 2964.59,-109.3 2734,-109.3"/>
<text text-anchor="middle" x="2849.29" y="-151.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2849.29" y="-134.3" font-family="Times,serif" font-size="14.00">IERC721Errors</text>
<text text-anchor="middle" x="2849.29" y="-117.5" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="2983,-109.3 2983,-167.7 3213.59,-167.7 3213.59,-109.3 2983,-109.3"/>
<text text-anchor="middle" x="3098.29" y="-151.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3098.29" y="-134.3" font-family="Times,serif" font-size="14.00">IERC1155Errors</text>
<text text-anchor="middle" x="3098.29" y="-117.5" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="2856.61,-3557.5 2856.61,-3791.9 3285.97,-3791.9 3285.97,-3557.5 2856.61,-3557.5"/>
<text text-anchor="middle" x="3071.29" y="-3775.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3071.29" y="-3758.5" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="3071.29" y="-3741.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2856.61,-3733.5 3285.97,-3733.5 "/>
<text text-anchor="start" x="2864.61" y="-3716.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2864.61" y="-3700.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="2864.61" y="-3683.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="2864.61" y="-3666.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="2864.61" y="-3649.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="2864.61" y="-3632.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="2864.61" y="-3616.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
<text text-anchor="start" x="2864.61" y="-3599.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2864.61" y="-3582.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="2864.61" y="-3565.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="3231.35,-71.7 3231.35,-205.3 3863.24,-205.3 3863.24,-71.7 3231.35,-71.7"/>
<text text-anchor="middle" x="3547.29" y="-188.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3547.29" y="-171.9" font-family="Times,serif" font-size="14.00">IERC20Permit</text>
<text text-anchor="middle" x="3547.29" y="-155.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="3231.35,-146.9 3863.24,-146.9 "/>
<text text-anchor="start" x="3239.35" y="-130.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3239.35" y="-113.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;permit(owner: address, spender: address, value: uint256, deadline: uint256, v: uint8, r: bytes32, s: bytes32)</text>
<text text-anchor="start" x="3239.35" y="-96.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nonces(owner: address): uint256</text>
<text text-anchor="start" x="3239.35" y="-79.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;DOMAIN_SEPARATOR(): bytes32</text>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="1447.86,-3269.3 1447.86,-3503.7 1946.73,-3503.7 1946.73,-3269.3 1447.86,-3269.3"/>
<text text-anchor="middle" x="1697.29" y="-3487.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1697.29" y="-3470.3" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="1697.29" y="-3453.5" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="1447.86,-3445.3 1946.73,-3445.3 "/>
<text text-anchor="start" x="1455.86" y="-3428.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1455.86" y="-3411.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revert(returndata: bytes)</text>
<text text-anchor="start" x="1455.86" y="-3395.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1455.86" y="-3378.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="1455.86" y="-3361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1455.86" y="-3344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="1455.86" y="-3327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1455.86" y="-3311.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionDelegateCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1455.86" y="-3294.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResultFromTarget(target: address, success: bool, returndata: bytes): bytes</text>
<text text-anchor="start" x="1455.86" y="-3277.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes): bytes</text>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="2458,-3319.7 2458,-3453.3 2688.59,-3453.3 2688.59,-3319.7 2458,-3319.7"/>
<text text-anchor="middle" x="2573.29" y="-3436.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="2573.29" y="-3419.9" font-family="Times,serif" font-size="14.00">Context</text>
<text text-anchor="middle" x="2573.29" y="-3403.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2458,-3394.9 2688.59,-3394.9 "/>
<text text-anchor="start" x="2466" y="-3378.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2466" y="-3361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="2466" y="-3344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="2466" y="-3327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="304.87,-2594.7 304.87,-2694.7 581.72,-2694.7 581.72,-2594.7 304.87,-2594.7"/>
<text text-anchor="middle" x="443.29" y="-2678.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="443.29" y="-2661.3" font-family="Times,serif" font-size="14.00">IERC165</text>
<text text-anchor="middle" x="443.29" y="-2644.5" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="304.87,-2636.3 581.72,-2636.3 "/>
<text text-anchor="start" x="312.87" y="-2619.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="312.87" y="-2602.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportsInterface(interfaceId: bytes4): bool</text>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="1829.44,-2435.1 1829.44,-2854.3 2331.15,-2854.3 2331.15,-2435.1 1829.44,-2435.1"/>
<text text-anchor="middle" x="2080.29" y="-2837.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2080.29" y="-2820.9" font-family="Times,serif" font-size="14.00">Math</text>
<text text-anchor="middle" x="2080.29" y="-2804.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="1829.44,-2795.9 2331.15,-2795.9 "/>
<text text-anchor="start" x="1837.44" y="-2779.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1837.44" y="-2762.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryAdd(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="1837.44" y="-2745.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;trySub(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="1837.44" y="-2728.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMul(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="1837.44" y="-2712.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryDiv(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="1837.44" y="-2695.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMod(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="1837.44" y="-2678.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;max(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2661.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;min(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2644.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;average(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2628.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;ceilDiv(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2611.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256): (result: uint256)</text>
<text text-anchor="start" x="1837.44" y="-2594.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="1837.44" y="-2577.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2560.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="1837.44" y="-2544.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2527.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="1837.44" y="-2510.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2493.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="1837.44" y="-2476.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256): uint256</text>
<text text-anchor="start" x="1837.44" y="-2460.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="1837.44" y="-2443.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;unsignedRoundsUp(rounding: Rounding): bool</text>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="1965,-3319.7 1965,-3453.3 2195.59,-3453.3 2195.59,-3319.7 1965,-3319.7"/>
<text text-anchor="middle" x="2080.29" y="-3436.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="2080.29" y="-3419.9" font-family="Times,serif" font-size="14.00">Rounding</text>
<text text-anchor="middle" x="2080.29" y="-3403.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="1965,-3394.9 2195.59,-3394.9 "/>
<text text-anchor="start" x="1973" y="-3378.3" font-family="Times,serif" font-size="14.00">Floor: 0</text>
<text text-anchor="start" x="1973" y="-3361.5" font-family="Times,serif" font-size="14.00">Ceil: 1</text>
<text text-anchor="start" x="1973" y="-3344.7" font-family="Times,serif" font-size="14.00">Trunc: 2</text>
<text text-anchor="start" x="1973" y="-3327.9" font-family="Times,serif" font-size="14.00">Expand: 3</text>
</g>
<!-- 9&#45;&gt;10 -->
<g id="edge2" class="edge">
<title>9&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2074.15,-2854.73C2072.93,-3006.49 2073.55,-3203.11 2075.99,-3309.33"/>
<polygon fill="black" stroke="black" points="2072.5,-3309.72 2076.24,-3319.63 2079.5,-3309.55 2072.5,-3309.72"/>
</g>
<!-- 10&#45;&gt;9 -->
<g id="edge1" class="edge">
<title>10&#45;&gt;9</title>
<path fill="none" stroke="black" d="M2084.35,-3319.63C2086.93,-3218.88 2087.66,-3021.87 2086.53,-2866.77"/>
<polygon fill="black" stroke="black" points="2086.53,-2866.73 2082.49,-2860.76 2086.44,-2854.73 2090.49,-2860.7 2086.53,-2866.73"/>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="921.41,-2489.9 921.41,-2799.5 1235.18,-2799.5 1235.18,-2489.9 921.41,-2489.9"/>
<text text-anchor="middle" x="1078.29" y="-2782.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1078.29" y="-2766.1" font-family="Times,serif" font-size="14.00">Initializable</text>
<text text-anchor="middle" x="1078.29" y="-2749.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="921.41,-2741.1 1235.18,-2741.1 "/>
<text text-anchor="start" x="929.41" y="-2724.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="929.41" y="-2707.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;INITIALIZABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="921.41,-2699.5 1235.18,-2699.5 "/>
<text text-anchor="start" x="929.41" y="-2682.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="929.41" y="-2666.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializableStorage(): ($: InitializableStorage)</text>
<text text-anchor="start" x="929.41" y="-2649.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="929.41" y="-2632.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkInitializing()</text>
<text text-anchor="start" x="929.41" y="-2615.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_disableInitializers()</text>
<text text-anchor="start" x="929.41" y="-2598.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializedVersion(): uint64</text>
<text text-anchor="start" x="929.41" y="-2582.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isInitializing(): bool</text>
<text text-anchor="start" x="929.41" y="-2565.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="929.41" y="-2548.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Initialized(version: uint64)</text>
<text text-anchor="start" x="929.41" y="-2531.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; initializer()</text>
<text text-anchor="start" x="929.41" y="-2514.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; reinitializer(version: uint64)</text>
<text text-anchor="start" x="929.41" y="-2498.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyInitializing()</text>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="963,-3336.5 963,-3436.5 1193.59,-3436.5 1193.59,-3336.5 963,-3336.5"/>
<text text-anchor="middle" x="1078.29" y="-3419.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="1078.29" y="-3403.1" font-family="Times,serif" font-size="14.00">InitializableStorage</text>
<text text-anchor="middle" x="1078.29" y="-3386.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="963,-3378.1 1193.59,-3378.1 "/>
<text text-anchor="start" x="971" y="-3361.5" font-family="Times,serif" font-size="14.00">_initialized: uint64</text>
<text text-anchor="start" x="971" y="-3344.7" font-family="Times,serif" font-size="14.00">_initializing: bool</text>
</g>
<!-- 11&#45;&gt;12 -->
<g id="edge4" class="edge">
<title>11&#45;&gt;12</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1072.69,-2799.89C1070.72,-2963.32 1071.29,-3213.12 1074.42,-3326.21"/>
<polygon fill="black" stroke="black" points="1070.92,-3326.51 1074.72,-3336.4 1077.92,-3326.3 1070.92,-3326.51"/>
</g>
<!-- 12&#45;&gt;11 -->
<g id="edge3" class="edge">
<title>12&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1081.87,-3336.4C1085.18,-3231.38 1085.9,-2980.8 1084.04,-2812.14"/>
<polygon fill="black" stroke="black" points="1084.04,-2811.89 1079.97,-2805.93 1083.9,-2799.89 1087.97,-2805.84 1084.04,-2811.89"/>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#f2f2f2" stroke="black" points="3881,-88.5 3881,-188.5 4111.59,-188.5 4111.59,-88.5 3881,-88.5"/>
<text text-anchor="middle" x="3996.29" y="-171.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3996.29" y="-155.1" font-family="Times,serif" font-size="14.00">IOracleProvider</text>
<text text-anchor="middle" x="3996.29" y="-138.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="3881,-130.1 4111.59,-130.1 "/>
<text text-anchor="start" x="3889" y="-113.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3889" y="-96.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPrice(asset: address): uint256</text>
</g>
<!-- 14 -->
<g id="node15" class="node">
<title>14</title>
<polygon fill="#f2f2f2" stroke="black" points="3698,-3645.5 3698,-3703.9 3928.59,-3703.9 3928.59,-3645.5 3698,-3645.5"/>
<text text-anchor="middle" x="3813.29" y="-3687.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="3813.29" y="-3670.5" font-family="Times,serif" font-size="14.00">RiskEngineError</text>
<text text-anchor="middle" x="3813.29" y="-3653.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
</g>
<!-- 15 -->
<g id="node16" class="node">
<title>15</title>
<polygon fill="#f2f2f2" stroke="black" points="3683.78,-3252.5 3683.78,-3520.5 3942.81,-3520.5 3942.81,-3252.5 3683.78,-3252.5"/>
<text text-anchor="middle" x="3813.29" y="-3503.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="3813.29" y="-3487.1" font-family="Times,serif" font-size="14.00">Error</text>
<text text-anchor="middle" x="3813.29" y="-3470.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="3683.78,-3462.1 3942.81,-3462.1 "/>
<text text-anchor="start" x="3691.78" y="-3445.5" font-family="Times,serif" font-size="14.00">NO_ERROR: 0</text>
<text text-anchor="start" x="3691.78" y="-3428.7" font-family="Times,serif" font-size="14.00">RISKENGINE_MISMATCH: 1</text>
<text text-anchor="start" x="3691.78" y="-3411.9" font-family="Times,serif" font-size="14.00">INSUFFICIENT_SHORTFALL: 2</text>
<text text-anchor="start" x="3691.78" y="-3395.1" font-family="Times,serif" font-size="14.00">INSUFFICIENT_LIQUIDITY: 3</text>
<text text-anchor="start" x="3691.78" y="-3378.3" font-family="Times,serif" font-size="14.00">MARKET_NOT_LISTED: 4</text>
<text text-anchor="start" x="3691.78" y="-3361.5" font-family="Times,serif" font-size="14.00">PRICE_ERROR: 5</text>
<text text-anchor="start" x="3691.78" y="-3344.7" font-family="Times,serif" font-size="14.00">TOO_MUCH_REPAY: 6</text>
<text text-anchor="start" x="3691.78" y="-3327.9" font-family="Times,serif" font-size="14.00">SUPPLY_CAP_EXCEEDED: 7</text>
<text text-anchor="start" x="3691.78" y="-3311.1" font-family="Times,serif" font-size="14.00">BORROW_CAP_EXCEEDED: 8</text>
<text text-anchor="start" x="3691.78" y="-3294.3" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_AS_COLLATERAL: 9</text>
<text text-anchor="start" x="3691.78" y="-3277.5" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_TO_BORROW: 10</text>
<text text-anchor="start" x="3691.78" y="-3260.7" font-family="Times,serif" font-size="14.00">EMODE_NOT_ALLOWED: 11</text>
</g>
<!-- 15&#45;&gt;14 -->
<g id="edge5" class="edge">
<title>15&#45;&gt;14</title>
<path fill="none" stroke="black" d="M3813.29,-3520.55C3813.29,-3561.49 3813.29,-3603.54 3813.29,-3633.19"/>
<polygon fill="black" stroke="black" points="3813.29,-3633.5 3817.29,-3639.5 3813.29,-3645.5 3809.29,-3639.5 3813.29,-3633.5"/>
</g>
<!-- 16 -->
<g id="node17" class="node">
<title>16</title>
<polygon fill="#f2f2f2" stroke="black" points="2956,-3319.7 2956,-3453.3 3186.59,-3453.3 3186.59,-3319.7 2956,-3319.7"/>
<text text-anchor="middle" x="3071.29" y="-3436.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3071.29" y="-3419.9" font-family="Times,serif" font-size="14.00">IERC20Metadata</text>
<text text-anchor="middle" x="3071.29" y="-3403.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2956,-3394.9 3186.59,-3394.9 "/>
<text text-anchor="start" x="2964" y="-3378.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2964" y="-3361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="2964" y="-3344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="2964" y="-3327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
</g>
<!-- 16&#45;&gt;4 -->
<g id="edge6" class="edge">
<title>16&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3071.29,-3453.52C3071.29,-3475.71 3071.29,-3501.42 3071.29,-3527.28"/>
<polygon fill="none" stroke="black" points="3060.79,-3527.49 3071.29,-3557.49 3081.79,-3527.49 3060.79,-3527.49"/>
</g>
<!-- 17 -->
<g id="node18" class="node">
<title>17</title>
<polygon fill="#f2f2f2" stroke="black" points="1246.67,-1332.1 1246.67,-1499.3 1555.92,-1499.3 1555.92,-1332.1 1246.67,-1332.1"/>
<text text-anchor="middle" x="1401.29" y="-1482.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1401.29" y="-1465.9" font-family="Times,serif" font-size="14.00">ContextUpgradeable</text>
<text text-anchor="middle" x="1401.29" y="-1449.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="1246.67,-1440.9 1555.92,-1440.9 "/>
<text text-anchor="start" x="1254.67" y="-1424.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1254.67" y="-1407.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Context_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1254.67" y="-1390.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__Context_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="1254.67" y="-1373.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="1254.67" y="-1357.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="1254.67" y="-1340.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 17&#45;&gt;11 -->
<g id="edge7" class="edge">
<title>17&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1379.47,-1499.59C1327.92,-1695.45 1197.57,-2190.61 1126.43,-2460.86"/>
<polygon fill="none" stroke="black" points="1116.27,-2458.19 1118.79,-2489.87 1136.58,-2463.53 1116.27,-2458.19"/>
</g>
<!-- 18 -->
<g id="node19" class="node">
<title>18</title>
<polygon fill="#f2f2f2" stroke="black" points="2903.59,-2443.5 2903.59,-2845.9 3525,-2845.9 3525,-2443.5 2903.59,-2443.5"/>
<text text-anchor="middle" x="3214.29" y="-2829.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3214.29" y="-2812.5" font-family="Times,serif" font-size="14.00">IERC4626</text>
<text text-anchor="middle" x="3214.29" y="-2795.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2903.59,-2787.5 3525,-2787.5 "/>
<text text-anchor="start" x="2911.59" y="-2770.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2911.59" y="-2754.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): (assetTokenAddress: address)</text>
<text text-anchor="start" x="2911.59" y="-2737.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): (totalManagedAssets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2720.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2703.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2686.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(receiver: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2670.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2653.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(assets: uint256, receiver: address): (shares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2636.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): (maxShares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2619.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2602.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(shares: uint256, receiver: address): (assets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2586.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2569.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2552.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): (shares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2535.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2518.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2502.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): (assets: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2485.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2911.59" y="-2468.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Deposit(sender: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="2911.59" y="-2451.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Withdraw(sender: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
</g>
<!-- 18&#45;&gt;4 -->
<g id="edge8" class="edge">
<title>18&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3245.97,-2846.3C3266.43,-3028.26 3276.1,-3300.91 3195.29,-3521 3194.16,-3524.09 3192.94,-3527.15 3191.63,-3530.21"/>
<polygon fill="none" stroke="black" points="3182.22,-3525.56 3177.91,-3557.05 3200.91,-3535.12 3182.22,-3525.56"/>
</g>
<!-- 18&#45;&gt;16 -->
<g id="edge9" class="edge">
<title>18&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3175.6,-2845.9C3147.73,-2990.08 3111.37,-3178.17 3089.76,-3289.95"/>
<polygon fill="none" stroke="black" points="3079.4,-3288.24 3084.01,-3319.69 3100.02,-3292.23 3079.4,-3288.24"/>
</g>
<!-- 19 -->
<g id="node20" class="node">
<title>19</title>
<polygon fill="#f2f2f2" stroke="black" points="286.72,-1340.5 286.72,-1490.9 599.87,-1490.9 599.87,-1340.5 286.72,-1340.5"/>
<text text-anchor="middle" x="443.29" y="-1474.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="443.29" y="-1457.5" font-family="Times,serif" font-size="14.00">ERC165Upgradeable</text>
<text text-anchor="middle" x="443.29" y="-1440.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="286.72,-1432.5 599.87,-1432.5 "/>
<text text-anchor="start" x="294.72" y="-1415.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="294.72" y="-1399.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__ERC165_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="294.72" y="-1382.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__ERC165_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="294.72" y="-1365.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="294.72" y="-1348.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;supportsInterface(interfaceId: bytes4): bool</text>
</g>
<!-- 19&#45;&gt;8 -->
<g id="edge11" class="edge">
<title>19&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M443.29,-1491.15C443.29,-1708.54 443.29,-2341.1 443.29,-2564.46"/>
<polygon fill="none" stroke="black" points="432.79,-2564.64 443.29,-2594.64 453.79,-2564.64 432.79,-2564.64"/>
</g>
<!-- 19&#45;&gt;11 -->
<g id="edge10" class="edge">
<title>19&#45;&gt;11</title>
<path fill="none" stroke="black" d="M453.46,-1491.26C471.92,-1611.33 517.83,-1852.73 609.29,-2037.4 687.56,-2195.43 810.47,-2352.32 910.84,-2466.9"/>
<polygon fill="none" stroke="black" points="903.17,-2474.08 930.91,-2489.6 918.9,-2460.17 903.17,-2474.08"/>
</g>
<!-- 20 -->
<g id="node21" class="node">
<title>20</title>
<polygon fill="#f2f2f2" stroke="black" points="1291.2,-2535.9 1291.2,-2753.5 1811.39,-2753.5 1811.39,-2535.9 1291.2,-2535.9"/>
<text text-anchor="middle" x="1551.29" y="-2736.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1551.29" y="-2720.1" font-family="Times,serif" font-size="14.00">SafeERC20</text>
<text text-anchor="middle" x="1551.29" y="-2703.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="1291.2,-2695.1 1811.39,-2695.1 "/>
<text text-anchor="start" x="1299.2" y="-2678.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1299.2" y="-2661.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturn(token: IERC20, data: bytes)</text>
<text text-anchor="start" x="1299.2" y="-2644.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturnBool(token: IERC20, data: bytes): bool</text>
<text text-anchor="start" x="1299.2" y="-2628.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1299.2" y="-2611.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransfer(token: IERC20, to: address, value: uint256)</text>
<text text-anchor="start" x="1299.2" y="-2594.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransferFrom(token: IERC20, from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1299.2" y="-2577.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeIncreaseAllowance(token: IERC20, spender: address, value: uint256)</text>
<text text-anchor="start" x="1299.2" y="-2560.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeDecreaseAllowance(token: IERC20, spender: address, requestedDecrease: uint256)</text>
<text text-anchor="start" x="1299.2" y="-2544.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;forceApprove(token: IERC20, spender: address, value: uint256)</text>
</g>
<!-- 20&#45;&gt;4 -->
<g id="edge13" class="edge">
<title>20&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1490.36,-2753.82C1396.26,-2936.81 1245.61,-3308.35 1439.29,-3521 1532.16,-3622.97 2420.28,-3658.07 2845.94,-3669.1"/>
<polygon fill="black" stroke="black" points="2846.13,-3672.61 2856.22,-3669.36 2846.31,-3665.61 2846.13,-3672.61"/>
</g>
<!-- 20&#45;&gt;6 -->
<g id="edge12" class="edge">
<title>20&#45;&gt;6</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1572.6,-2753.67C1599.09,-2887.91 1644.24,-3116.66 1672.33,-3258.98"/>
<polygon fill="black" stroke="black" points="1668.95,-3259.95 1674.32,-3269.08 1675.82,-3258.59 1668.95,-3259.95"/>
</g>
<!-- 21 -->
<g id="node22" class="node">
<title>21</title>
<polygon fill="#f2f2f2" stroke="black" points="2387.3,-2397.5 2387.3,-2891.9 2847.29,-2891.9 2847.29,-2397.5 2387.3,-2397.5"/>
<text text-anchor="middle" x="2617.29" y="-2875.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="2617.29" y="-2858.5" font-family="Times,serif" font-size="14.00">ERC20</text>
<text text-anchor="middle" x="2617.29" y="-2841.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2387.3,-2833.5 2847.29,-2833.5 "/>
<text text-anchor="start" x="2395.3" y="-2816.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="2395.3" y="-2800.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_balances: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="2395.3" y="-2783.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_allowances: mapping(address=&gt;mapping(address=&gt;uint256))</text>
<text text-anchor="start" x="2395.3" y="-2766.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_totalSupply: uint256</text>
<text text-anchor="start" x="2395.3" y="-2749.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_name: string</text>
<text text-anchor="start" x="2395.3" y="-2732.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_symbol: string</text>
<polyline fill="none" stroke="black" points="2387.3,-2724.7 2847.29,-2724.7 "/>
<text text-anchor="start" x="2395.3" y="-2708.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2395.3" y="-2691.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="2395.3" y="-2674.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_update(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="2395.3" y="-2657.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_mint(account: address, value: uint256)</text>
<text text-anchor="start" x="2395.3" y="-2640.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_burn(account: address, value: uint256)</text>
<text text-anchor="start" x="2395.3" y="-2624.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="2395.3" y="-2607.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256, emitEvent: bool)</text>
<text text-anchor="start" x="2395.3" y="-2590.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_spendAllowance(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="2395.3" y="-2573.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2395.3" y="-2556.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(name_: string, symbol_: string)</text>
<text text-anchor="start" x="2395.3" y="-2540.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="2395.3" y="-2523.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="2395.3" y="-2506.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="2395.3" y="-2489.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="2395.3" y="-2472.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="2395.3" y="-2456.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="2395.3" y="-2439.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="2395.3" y="-2422.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="2395.3" y="-2405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
</g>
<!-- 21&#45;&gt;1 -->
<g id="edge17" class="edge">
<title>21&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2685.56,-2892.04C2728.54,-3047.18 2780.61,-3235.07 2806.33,-3327.88"/>
<polygon fill="none" stroke="black" points="2796.28,-3330.93 2814.41,-3357.04 2816.51,-3325.32 2796.28,-3330.93"/>
</g>
<!-- 21&#45;&gt;4 -->
<g id="edge15" class="edge">
<title>21&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2474.14,-2892.2C2384.39,-3082.64 2309.65,-3342.92 2449.29,-3521 2496.62,-3581.35 2673.57,-3621 2826.7,-3644.74"/>
<polygon fill="none" stroke="black" points="2825.23,-3655.14 2856.46,-3649.2 2828.35,-3634.37 2825.23,-3655.14"/>
</g>
<!-- 21&#45;&gt;7 -->
<g id="edge14" class="edge">
<title>21&#45;&gt;7</title>
<path fill="none" stroke="black" d="M2602.64,-2892.04C2594.55,-3028.16 2584.95,-3189.5 2578.99,-3289.7"/>
<polygon fill="none" stroke="black" points="2568.51,-3289.1 2577.21,-3319.67 2589.47,-3290.34 2568.51,-3289.1"/>
</g>
<!-- 21&#45;&gt;16 -->
<g id="edge16" class="edge">
<title>21&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2677.12,-2892.08C2714.53,-3002.74 2772.89,-3127.92 2861.29,-3216 2890.36,-3244.96 2912.65,-3228.52 2946.29,-3252 2964.83,-3264.94 2982.73,-3281.02 2998.91,-3297.48"/>
<polygon fill="none" stroke="black" points="2991.31,-3304.72 3019.45,-3319.5 3006.67,-3290.4 2991.31,-3304.72"/>
</g>
<!-- 22 -->
<g id="node23" class="node">
<title>22</title>
<polygon fill="#f2f2f2" stroke="black" points="741.85,-313.5 741.85,-757.5 1104.73,-757.5 1104.73,-313.5 741.85,-313.5"/>
<text text-anchor="middle" x="923.29" y="-740.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="923.29" y="-724.1" font-family="Times,serif" font-size="14.00">AccessControlUpgradeable</text>
<text text-anchor="middle" x="923.29" y="-707.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="741.85,-699.1 1104.73,-699.1 "/>
<text text-anchor="start" x="749.85" y="-682.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="749.85" y="-665.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;AccessControlStorageLocation: bytes32</text>
<text text-anchor="start" x="749.85" y="-648.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="749.85" y="-632.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;DEFAULT_ADMIN_ROLE: bytes32</text>
<polyline fill="none" stroke="black" points="741.85,-623.9 1104.73,-623.9 "/>
<text text-anchor="start" x="749.85" y="-607.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="749.85" y="-590.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getAccessControlStorage(): ($: AccessControlStorage)</text>
<text text-anchor="start" x="749.85" y="-573.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="749.85" y="-556.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__AccessControl_init() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="749.85" y="-540.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;__AccessControl_init_unchained() &lt;&lt;onlyInitializing&gt;&gt;</text>
<text text-anchor="start" x="749.85" y="-523.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkRole(role: bytes32)</text>
<text text-anchor="start" x="749.85" y="-506.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkRole(role: bytes32, account: address)</text>
<text text-anchor="start" x="749.85" y="-489.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setRoleAdmin(role: bytes32, adminRole: bytes32)</text>
<text text-anchor="start" x="749.85" y="-472.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_grantRole(role: bytes32, account: address): bool</text>
<text text-anchor="start" x="749.85" y="-456.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revokeRole(role: bytes32, account: address): bool</text>
<text text-anchor="start" x="749.85" y="-439.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="749.85" y="-422.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyRole(role: bytes32)</text>
<text text-anchor="start" x="749.85" y="-405.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;supportsInterface(interfaceId: bytes4): bool</text>
<text text-anchor="start" x="749.85" y="-388.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;hasRole(role: bytes32, account: address): bool</text>
<text text-anchor="start" x="749.85" y="-372.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getRoleAdmin(role: bytes32): bytes32</text>
<text text-anchor="start" x="749.85" y="-355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;grantRole(role: bytes32, account: address) &lt;&lt;onlyRole&gt;&gt;</text>
<text text-anchor="start" x="749.85" y="-338.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;revokeRole(role: bytes32, account: address) &lt;&lt;onlyRole&gt;&gt;</text>
<text text-anchor="start" x="749.85" y="-321.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceRole(role: bytes32, callerConfirmation: address)</text>
</g>
<!-- 22&#45;&gt;0 -->
<g id="edge22" class="edge">
<title>22&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M923.29,-757.54C923.29,-917.32 923.29,-1128.73 923.29,-1268.08"/>
<polygon fill="none" stroke="black" points="912.79,-1268.4 923.29,-1298.4 933.79,-1268.4 912.79,-1268.4"/>
</g>
<!-- 22&#45;&gt;11 -->
<g id="edge20" class="edge">
<title>22&#45;&gt;11</title>
<path fill="none" stroke="black" d="M1104.81,-554.67C1256.21,-580.47 1463.11,-642.92 1565.29,-794 1874.89,-1251.75 1933.28,-1625.11 1565.29,-2037.4 1518.23,-2090.13 1303.51,-2028.06 1249.29,-2073.4 1137.16,-2167.17 1096.73,-2331.37 1083.01,-2459.57"/>
<polygon fill="none" stroke="black" points="1072.53,-2458.81 1080.2,-2489.65 1093.44,-2460.76 1072.53,-2458.81"/>
</g>
<!-- 22&#45;&gt;17 -->
<g id="edge21" class="edge">
<title>22&#45;&gt;17</title>
<path fill="none" stroke="black" d="M1104.89,-653.57C1154,-692.95 1202.99,-740.62 1237.29,-794 1338.71,-951.83 1377.66,-1170.43 1392.45,-1301.67"/>
<polygon fill="none" stroke="black" points="1382.04,-1303.12 1395.59,-1331.87 1402.93,-1300.94 1382.04,-1303.12"/>
</g>
<!-- 22&#45;&gt;19 -->
<g id="edge23" class="edge">
<title>22&#45;&gt;19</title>
<path fill="none" stroke="black" d="M741.8,-653.63C692.69,-693.01 643.67,-740.67 609.29,-794 505.3,-955.31 466.11,-1180.18 451.59,-1310.3"/>
<polygon fill="none" stroke="black" points="441.11,-1309.54 448.48,-1340.46 462,-1311.7 441.11,-1309.54"/>
</g>
<!-- 24 -->
<g id="node25" class="node">
<title>24</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-1374.1 0,-1457.3 230.59,-1457.3 230.59,-1374.1 0,-1374.1"/>
<text text-anchor="middle" x="115.29" y="-1440.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="115.29" y="-1423.9" font-family="Times,serif" font-size="14.00">AccessControlStorage</text>
<text text-anchor="middle" x="115.29" y="-1407.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-1398.9 230.59,-1398.9 "/>
<text text-anchor="start" x="8" y="-1382.3" font-family="Times,serif" font-size="14.00">_roles: mapping(bytes32=&gt;RoleData)</text>
</g>
<!-- 22&#45;&gt;24 -->
<g id="edge24" class="edge">
<title>22&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M741.48,-555.78C583.96,-582.43 362.07,-645.49 235.29,-794 91.88,-962 94.04,-1248.07 106.01,-1363.83"/>
<polygon fill="black" stroke="black" points="102.54,-1364.28 107.12,-1373.84 109.5,-1363.51 102.54,-1364.28"/>
</g>
<!-- 23 -->
<g id="node24" class="node">
<title>23</title>
<polygon fill="#f2f2f2" stroke="black" points="56,-2594.7 56,-2694.7 286.59,-2694.7 286.59,-2594.7 56,-2594.7"/>
<text text-anchor="middle" x="171.29" y="-2678.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="171.29" y="-2661.3" font-family="Times,serif" font-size="14.00">RoleData</text>
<text text-anchor="middle" x="171.29" y="-2644.5" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="56,-2636.3 286.59,-2636.3 "/>
<text text-anchor="start" x="64" y="-2619.7" font-family="Times,serif" font-size="14.00">hasRole: mapping(address=&gt;bool)</text>
<text text-anchor="start" x="64" y="-2602.9" font-family="Times,serif" font-size="14.00">adminRole: bytes32</text>
</g>
<!-- 23&#45;&gt;22 -->
<g id="edge18" class="edge">
<title>23&#45;&gt;22</title>
<path fill="none" stroke="black" d="M178.21,-2594.31C192.35,-2491.12 224.5,-2245.01 239.29,-2037.4 244.2,-1968.46 237.64,-850.61 277.29,-794 379.72,-647.77 579.35,-584.56 729.72,-557.25"/>
<polygon fill="black" stroke="black" points="730.01,-557.2 735.22,-552.21 741.83,-555.11 736.61,-560.09 730.01,-557.2"/>
</g>
<!-- 24&#45;&gt;22 -->
<g id="edge19" class="edge">
<title>24&#45;&gt;22</title>
<path fill="none" stroke="black" d="M115.63,-1373.84C112.31,-1265.44 105.55,-967.07 253.29,-794 373.04,-653.73 577.65,-589.69 729.36,-560.56"/>
<polygon fill="black" stroke="black" points="729.77,-560.48 734.93,-555.44 741.56,-558.26 736.4,-563.3 729.77,-560.48"/>
</g>
<!-- 24&#45;&gt;23 -->
<g id="edge25" class="edge">
<title>24&#45;&gt;23</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M117.16,-1457.52C125.42,-1638.67 158.81,-2370.22 168.59,-2584.47"/>
<polygon fill="black" stroke="black" points="165.1,-2584.75 169.05,-2594.58 172.09,-2584.43 165.1,-2584.75"/>
</g>
<!-- 25 -->
<g id="node26" class="node">
<title>25</title>
<polygon fill="#f2f2f2" stroke="black" points="2200.07,-1126.5 2200.07,-1704.9 2754.52,-1704.9 2754.52,-1126.5 2200.07,-1126.5"/>
<text text-anchor="middle" x="2477.29" y="-1688.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="2477.29" y="-1671.5" font-family="Times,serif" font-size="14.00">ERC4626</text>
<text text-anchor="middle" x="2477.29" y="-1654.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2200.07,-1646.5 2754.52,-1646.5 "/>
<text text-anchor="start" x="2208.07" y="-1629.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="2208.07" y="-1613.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_asset: IERC20</text>
<text text-anchor="start" x="2208.07" y="-1596.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_underlyingDecimals: uint8</text>
<polyline fill="none" stroke="black" points="2200.07,-1588.1 2754.52,-1588.1 "/>
<text text-anchor="start" x="2208.07" y="-1571.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="2208.07" y="-1554.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_tryGetAssetDecimals(asset_: IERC20): (bool, uint8)</text>
<text text-anchor="start" x="2208.07" y="-1537.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2208.07" y="-1521.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToShares(assets: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="2208.07" y="-1504.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToAssets(shares: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="2208.07" y="-1487.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_deposit(caller: address, receiver: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="2208.07" y="-1470.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_withdraw(caller: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="2208.07" y="-1453.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_decimalsOffset(): uint8</text>
<text text-anchor="start" x="2208.07" y="-1437.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2208.07" y="-1420.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(asset_: IERC20)</text>
<text text-anchor="start" x="2208.07" y="-1403.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="2208.07" y="-1386.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="2208.07" y="-1369.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="2208.07" y="-1353.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="2208.07" y="-1336.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="2208.07" y="-1319.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxDeposit(address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1302.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxMint(address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1285.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1269.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxRedeem(owner: address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1252.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewDeposit(assets: uint256): uint256</text>
<text text-anchor="start" x="2208.07" y="-1235.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewMint(shares: uint256): uint256</text>
<text text-anchor="start" x="2208.07" y="-1218.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewWithdraw(assets: uint256): uint256</text>
<text text-anchor="start" x="2208.07" y="-1201.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewRedeem(shares: uint256): uint256</text>
<text text-anchor="start" x="2208.07" y="-1185.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;deposit(assets: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1168.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mint(shares: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1151.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="2208.07" y="-1134.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): uint256</text>
</g>
<!-- 25&#45;&gt;4 -->
<g id="edge29" class="edge">
<title>25&#45;&gt;4</title>
<path fill="none" stroke="black" d="M2202.85,-1705.24C2066.45,-1829.72 1891.56,-1963.49 1707.29,-2037.4 1619.32,-2072.69 1346.42,-2003.61 1282.29,-2073.4 1239.34,-2120.14 1276.14,-3152.82 1282.29,-3216 1295.69,-3353.5 1239.56,-3428.63 1342.29,-3521 1451.89,-3619.54 2403.7,-3656.65 2846.49,-3668.67"/>
<polygon fill="black" stroke="black" points="2846.44,-3672.17 2856.53,-3668.94 2846.63,-3665.17 2846.44,-3672.17"/>
</g>
<!-- 25&#45;&gt;9 -->
<g id="edge28" class="edge">
<title>25&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2383.96,-1705.15C2312.7,-1925.41 2216.01,-2224.25 2151.05,-2425.02"/>
<polygon fill="black" stroke="black" points="2147.63,-2424.21 2147.88,-2434.8 2154.29,-2426.36 2147.63,-2424.21"/>
</g>
<!-- 25&#45;&gt;10 -->
<g id="edge31" class="edge">
<title>25&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2426.39,-1705.14C2408.17,-1819.45 2389.4,-1952.27 2378.29,-2073.4 2372.49,-2136.65 2371.47,-3160.67 2340.29,-3216 2310.29,-3269.25 2255.82,-3308.59 2204.46,-3335.89"/>
<polygon fill="black" stroke="black" points="2202.84,-3332.79 2195.59,-3340.51 2206.07,-3339 2202.84,-3332.79"/>
</g>
<!-- 25&#45;&gt;16 -->
<g id="edge30" class="edge">
<title>25&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2558.24,-1705.12C2603.01,-1820.97 2669.32,-1947.97 2764.29,-2037.4 2796.26,-2067.5 2830.94,-2037.55 2856.29,-2073.4 2929.64,-2177.11 2833.48,-3104.48 2894.29,-3216 2907.75,-3240.68 2925.01,-3233.64 2946.29,-3252 2967.49,-3270.29 2988.91,-3291.82 3007.89,-3312.18"/>
<polygon fill="black" stroke="black" points="3005.41,-3314.65 3014.77,-3319.62 3010.55,-3309.9 3005.41,-3314.65"/>
</g>
<!-- 25&#45;&gt;18 -->
<g id="edge27" class="edge">
<title>25&#45;&gt;18</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2552.65,-1705.14C2596.73,-1822.93 2664.05,-1951.4 2764.29,-2037.4 2808.17,-2075.04 2843.86,-2037.65 2889.29,-2073.4 3000.62,-2160.99 3080.46,-2297.97 3133.14,-2415.74"/>
<polygon fill="none" stroke="black" points="3123.52,-2419.95 3145.13,-2443.26 3142.78,-2411.57 3123.52,-2419.95"/>
</g>
<!-- 25&#45;&gt;20 -->
<g id="edge32" class="edge">
<title>25&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2199.73,-1653.44C2073.25,-1770.91 1928.07,-1920.24 1820.29,-2073.4 1718.78,-2217.66 1638.48,-2406.27 1592.91,-2526.23"/>
<polygon fill="black" stroke="black" points="1589.55,-2525.23 1589.29,-2535.83 1596.1,-2527.71 1589.55,-2525.23"/>
</g>
<!-- 25&#45;&gt;21 -->
<g id="edge26" class="edge">
<title>25&#45;&gt;21</title>
<path fill="none" stroke="black" d="M2510.21,-1705.15C2533.03,-1905.19 2563.25,-2170.07 2585.77,-2367.41"/>
<polygon fill="none" stroke="black" points="2575.34,-2368.63 2589.17,-2397.25 2596.2,-2366.25 2575.34,-2368.63"/>
</g>
<!-- 26 -->
<g id="node27" class="node">
<title>26</title>
<polygon fill="#f2f2f2" stroke="black" points="3021.55,-794.5 3021.55,-2036.9 3849.04,-2036.9 3849.04,-794.5 3021.55,-794.5"/>
<text text-anchor="middle" x="3435.29" y="-2020.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3435.29" y="-2003.5" font-family="Times,serif" font-size="14.00">IPToken</text>
<text text-anchor="middle" x="3435.29" y="-1986.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="3021.55,-1978.5 3849.04,-1978.5 "/>
<text text-anchor="start" x="3029.55" y="-1961.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3029.55" y="-1945.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(tokenAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1928.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(mintAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1911.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(redeemTokens: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1894.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(redeemAmount: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1877.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrow(borrowAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1861.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowOnBehalfOf(onBehalfOf: address, borrowAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1844.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrow(repayAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1827.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowOnBehalfOf(onBehalfOf: address, repayAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1810.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrow(borrower: address, repayAmount: uint256, pTokenCollateral: IPToken)</text>
<text text-anchor="start" x="3029.55" y="-1793.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrueInterest()</text>
<text text-anchor="start" x="3029.55" y="-1777.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seize(liquidator: address, borrower: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1760.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;addReserves(addAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1743.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveFactor(newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1726.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowRateMax(newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1709.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setProtocolSeizeShare(newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1693.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesEmergency(reduceAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1676.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesOwner(reduceAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1659.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesConfigurator(reduceAmount: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1642.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;sweepToken(token: IERC20)</text>
<text text-anchor="start" x="3029.55" y="-1625.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRateMaxMantissa(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1609.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrualBlockTimestamp(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1592.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateCurrent(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1575.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOfUnderlying(owner: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1558.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountSnapshot(account: address): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="3029.55" y="-1541.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrowsCurrent(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1525.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReservesCurrent(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1508.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReservesCurrent(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1491.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReservesCurrent(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1474.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceCurrent(account: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1457.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceStored(account: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1441.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateStored(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1424.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getCash(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1407.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRatePerSecond(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1390.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyRatePerSecond(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1373.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrows(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1357.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReserves(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1340.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReserves(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1323.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReserves(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1306.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowIndex(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1289.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reserveFactorMantissa(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1273.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;riskEngine(): IRiskEngine</text>
<text text-anchor="start" x="3029.55" y="-1256.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1239.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="3029.55" y="-1222.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="3029.55" y="-1205.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="3029.55" y="-1189.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="3029.55" y="-1172.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;protocolSeizeShareMantissa(): uint256</text>
<text text-anchor="start" x="3029.55" y="-1155.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="3029.55" y="-1138.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="3029.55" y="-1121.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1105.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(account: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1088.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1071.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="3029.55" y="-1054.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1037.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1021.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="3029.55" y="-1004.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="3029.55" y="-987.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="3029.55" y="-970.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3029.55" y="-953.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewRiskEngine(oldRiskEngine: IRiskEngine, newRiskEngine: IRiskEngine)</text>
<text text-anchor="start" x="3029.55" y="-937.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Borrow(borrower: address, onBehalfOf: address, borrowAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="3029.55" y="-920.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RepayBorrow(payer: address, onBehalfOf: address, repayAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="3029.55" y="-903.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveFactor(oldReserveFactorMantissa: uint256, newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="3029.55" y="-886.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowRateMax(oldBorrowRateMaxMantissa: uint256, newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="3029.55" y="-869.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewProtocolSeizeShare(oldProtocolSeizeShareMantissa: uint256, newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="3029.55" y="-853.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AccrueInterest(cashPrior: uint256, totalReserves: uint256, borrowIndex: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="3029.55" y="-836.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; LiquidateBorrow(liquidator: address, borrower: address, repayAmount: uint256, pTokenCollateral: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="3029.55" y="-819.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesAdded(benefactor: address, addAmount: uint256, newTotalReserves: uint256)</text>
<text text-anchor="start" x="3029.55" y="-802.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesReduced(admin: address, reduceAmount: uint256, newTotalReserves: uint256)</text>
</g>
<!-- 26&#45;&gt;4 -->
<g id="edge36" class="edge">
<title>26&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3531.35,-2037.31C3532.42,-2049.43 3533.4,-2061.47 3534.29,-2073.4 3543.78,-2200 3582.1,-3098.39 3534.29,-3216 3478.5,-3353.26 3358.45,-3469.96 3254.58,-3551.16"/>
<polygon fill="black" stroke="black" points="3252.25,-3548.54 3246.49,-3557.44 3256.54,-3554.07 3252.25,-3548.54"/>
</g>
<!-- 26&#45;&gt;18 -->
<g id="edge33" class="edge">
<title>26&#45;&gt;18</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3323.58,-2036.94C3299.32,-2171.62 3275.14,-2305.86 3255.72,-2413.7"/>
<polygon fill="none" stroke="black" points="3245.35,-2412.01 3250.37,-2443.4 3266.02,-2415.74 3245.35,-2412.01"/>
</g>
<!-- 26&#45;&gt;26 -->
<g id="edge35" class="edge">
<title>26&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3849.2,-1331.82C3860.61,-1354.87 3867.04,-1382.83 3867.04,-1415.7 3867.04,-1444.33 3862.16,-1469.24 3853.36,-1490.42"/>
<polygon fill="black" stroke="black" points="3850.15,-1489.03 3849.2,-1499.58 3856.53,-1491.92 3850.15,-1489.03"/>
</g>
<!-- 27 -->
<g id="node28" class="node">
<title>27</title>
<polygon fill="#f2f2f2" stroke="black" points="3581.76,-2073.9 3581.76,-3215.5 4574.83,-3215.5 4574.83,-2073.9 3581.76,-2073.9"/>
<text text-anchor="middle" x="4078.29" y="-3198.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4078.29" y="-3182.1" font-family="Times,serif" font-size="14.00">IRiskEngine</text>
<text text-anchor="middle" x="4078.29" y="-3165.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="3581.76,-3157.1 4574.83,-3157.1 "/>
<text text-anchor="start" x="3589.76" y="-3140.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3589.76" y="-3123.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3589.76" y="-3106.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;switchEMode(newCategoryId: uint8)</text>
<text text-anchor="start" x="3589.76" y="-3090.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;enterMarkets(pTokens: address[]): uint256[]</text>
<text text-anchor="start" x="3589.76" y="-3073.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exitMarket(pTokenAddress: address)</text>
<text text-anchor="start" x="3589.76" y="-3056.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;updateDelegate(delegate: address, approved: bool)</text>
<text text-anchor="start" x="3589.76" y="-3039.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintVerify(account: address)</text>
<text text-anchor="start" x="3589.76" y="-3022.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowVerify(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3589.76" y="-3006.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowAllowed(pToken: address, borrower: address, borrowAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2989.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setOracle(newOracle: address)</text>
<text text-anchor="start" x="3589.76" y="-2972.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureEMode(categoryId: uint8, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3589.76" y="-2955.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setCloseFactor(pTokenAddress: address, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="3589.76" y="-2938.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureMarket(pToken: IPToken, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3589.76" y="-2922.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportMarket(pToken: IPToken)</text>
<text text-anchor="start" x="3589.76" y="-2905.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportEMode(categoryId: uint8, isAllowed: bool, pTokens: address[], collateralPermissions: bool[], borrowPermissions: bool[])</text>
<text text-anchor="start" x="3589.76" y="-2888.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketBorrowCaps(pTokens: IPToken[], newBorrowCaps: uint256[])</text>
<text text-anchor="start" x="3589.76" y="-2871.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketSupplyCaps(pTokens: IPToken[], newSupplyCaps: uint256[])</text>
<text text-anchor="start" x="3589.76" y="-2854.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMintPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="3589.76" y="-2838.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="3589.76" y="-2821.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setTransferPaused(state: bool): bool</text>
<text text-anchor="start" x="3589.76" y="-2804.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setSeizePaused(state: bool): bool</text>
<text text-anchor="start" x="3589.76" y="-2787.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAssetsIn(account: address): IPToken[]</text>
<text text-anchor="start" x="3589.76" y="-2770.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getReserveShares(): (ownerShareMantissa: uint256, configuratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3589.76" y="-2754.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkCollateralMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="3589.76" y="-2737.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkBorrowMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="3589.76" y="-2720.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accountCategory(account: address): uint8</text>
<text text-anchor="start" x="3589.76" y="-2703.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3589.76" y="-2686.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountBorrowLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3589.76" y="-2670.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getHypotheticalAccountLiquidity(account: address, pTokenModify: address, redeemTokens: uint256, borrowAmount: uint256): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3589.76" y="-2653.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateCalculateSeizeTokens(borrower: address, pTokenBorrowed: address, pTokenCollateral: address, actualRepayAmount: uint256): (RiskEngineError.Error, uint256)</text>
<text text-anchor="start" x="3589.76" y="-2636.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;delegateAllowed(user: address, delegate: address): bool</text>
<text text-anchor="start" x="3589.76" y="-2619.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAllMarkets(): IPToken[]</text>
<text text-anchor="start" x="3589.76" y="-2602.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;isDeprecated(pToken: IPToken): bool</text>
<text text-anchor="start" x="3589.76" y="-2586.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(pToken: address, account: address): uint256</text>
<text text-anchor="start" x="3589.76" y="-2569.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintAllowed(account: address, pToken: address, mintAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2552.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeemAllowed(pToken: address, redeemer: address, redeemTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2535.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowAllowed(pToken: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2518.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrowAllowed(pTokenBorrowed: address, pTokenCollateral: address, borrower: address, repayAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2502.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seizeAllowed(pTokenCollateral: address, pTokenBorrowed: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2485.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferAllowed(pToken: address, src: address, transferTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3589.76" y="-2468.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;oracle(): address</text>
<text text-anchor="start" x="3589.76" y="-2451.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;collateralFactor(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="3589.76" y="-2434.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationThreshold(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="3589.76" y="-2418.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationIncentive(categoryId: uint8, pToken: address): uint256</text>
<text text-anchor="start" x="3589.76" y="-2401.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;closeFactor(pToken: address): uint256</text>
<text text-anchor="start" x="3589.76" y="-2384.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyCap(pToken: address): uint256</text>
<text text-anchor="start" x="3589.76" y="-2367.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowCap(pToken: address): uint256</text>
<text text-anchor="start" x="3589.76" y="-2350.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;emodeMarkets(categoryId: uint8): (collateralTokens: address[], borrowTokens: address[])</text>
<text text-anchor="start" x="3589.76" y="-2334.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3589.76" y="-2317.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewEModeConfiguration(categoryId: uint8, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3589.76" y="-2300.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewMarketConfiguration(pToken: IPToken, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3589.76" y="-2283.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewOracleEngine(oldOracleEngine: address, newOracleEngine: address)</text>
<text text-anchor="start" x="3589.76" y="-2266.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3589.76" y="-2250.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketListed(pToken: IPToken)</text>
<text text-anchor="start" x="3589.76" y="-2233.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeSwitched(account: address, oldCategory: uint8, newCategory: uint8)</text>
<text text-anchor="start" x="3589.76" y="-2216.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeUpdated(categoryId: uint8, pToken: address, allowed: bool, collateralStatus: bool, borrowStatus: bool)</text>
<text text-anchor="start" x="3589.76" y="-2199.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketEntered(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3589.76" y="-2182.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketExited(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3589.76" y="-2166.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewCloseFactor(pToken: address, oldCloseFactorMantissa: uint256, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="3589.76" y="-2149.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(action: string, pauseState: bool)</text>
<text text-anchor="start" x="3589.76" y="-2132.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(pToken: IPToken, action: string, pauseState: bool)</text>
<text text-anchor="start" x="3589.76" y="-2115.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowCap(pToken: IPToken, newBorrowCap: uint256)</text>
<text text-anchor="start" x="3589.76" y="-2098.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewSupplyCap(pToken: IPToken, newSupplyCap: uint256)</text>
<text text-anchor="start" x="3589.76" y="-2082.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; DelegateUpdated(approver: address, delegate: address, approved: bool)</text>
</g>
<!-- 26&#45;&gt;27 -->
<g id="edge34" class="edge">
<title>26&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3753.58,-2036.94C3758.37,-2046.08 3763.16,-2055.23 3767.95,-2064.36"/>
<polygon fill="black" stroke="black" points="3765,-2066.26 3772.74,-2073.48 3771.19,-2063 3765,-2066.26"/>
</g>
<!-- 27&#45;&gt;14 -->
<g id="edge40" class="edge">
<title>27&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4276.93,-3215.63C4283.43,-3323.76 4265.72,-3431.07 4204.29,-3521 4145.26,-3607.42 4029.41,-3644.94 3939.07,-3661.22"/>
<polygon fill="black" stroke="black" points="3938.27,-3657.81 3929.02,-3662.96 3939.47,-3664.7 3938.27,-3657.81"/>
</g>
<!-- 27&#45;&gt;15 -->
<g id="edge41" class="edge">
<title>27&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3874.09,-3215.77C3870.75,-3225.09 3867.5,-3234.16 3864.36,-3242.95"/>
<polygon fill="black" stroke="black" points="3861.06,-3241.78 3860.98,-3252.38 3867.65,-3244.14 3861.06,-3241.78"/>
</g>
<!-- 27&#45;&gt;26 -->
<g id="edge39" class="edge">
<title>27&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3786.21,-2073.48C3781.44,-2064.36 3776.65,-2055.22 3771.86,-2046.08"/>
<polygon fill="black" stroke="black" points="3774.82,-2044.17 3767.08,-2036.94 3768.62,-2047.42 3774.82,-2044.17"/>
</g>
<!-- 28 -->
<g id="node29" class="node">
<title>28</title>
<polygon fill="#f2f2f2" stroke="black" points="3961.22,-3328.1 3961.22,-3444.9 4195.37,-3444.9 4195.37,-3328.1 3961.22,-3328.1"/>
<text text-anchor="middle" x="4078.29" y="-3428.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="4078.29" y="-3411.5" font-family="Times,serif" font-size="14.00">BaseConfiguration</text>
<text text-anchor="middle" x="4078.29" y="-3394.7" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="3961.22,-3386.5 4195.37,-3386.5 "/>
<text text-anchor="start" x="3969.22" y="-3369.9" font-family="Times,serif" font-size="14.00">collateralFactorMantissa: uint256</text>
<text text-anchor="start" x="3969.22" y="-3353.1" font-family="Times,serif" font-size="14.00">liquidationThresholdMantissa: uint256</text>
<text text-anchor="start" x="3969.22" y="-3336.3" font-family="Times,serif" font-size="14.00">liquidationIncentiveMantissa: uint256</text>
</g>
<!-- 27&#45;&gt;28 -->
<g id="edge38" class="edge">
<title>27&#45;&gt;28</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4072.51,-3215.77C4072.93,-3254.96 4073.49,-3289.86 4074.19,-3317.67"/>
<polygon fill="black" stroke="black" points="4070.7,-3318.07 4074.47,-3327.97 4077.7,-3317.88 4070.7,-3318.07"/>
</g>
<!-- 28&#45;&gt;27 -->
<g id="edge37" class="edge">
<title>28&#45;&gt;27</title>
<path fill="none" stroke="black" d="M4082.12,-3327.97C4082.87,-3301.39 4083.48,-3267 4083.94,-3227.8"/>
<polygon fill="black" stroke="black" points="4083.94,-3227.77 4080.01,-3221.73 4084.07,-3215.77 4088.01,-3221.82 4083.94,-3227.77"/>
</g>
<!-- 29 -->
<g id="node30" class="node">
<title>29</title>
<polygon fill="#f2f2f2" stroke="black" points="2466.76,-443.5 2466.76,-627.5 3309.83,-627.5 3309.83,-443.5 2466.76,-443.5"/>
<text text-anchor="middle" x="2888.29" y="-610.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2888.29" y="-594.1" font-family="Times,serif" font-size="14.00">IOracleEngine</text>
<text text-anchor="middle" x="2888.29" y="-577.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2466.76,-569.1 3309.83,-569.1 "/>
<text text-anchor="start" x="2474.76" y="-552.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2474.76" y="-535.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setAssetConfig(asset: address, mainOracle: address, fallbackOracle: address, lowerBoundRatio: uint256, upperBoundRatio: uint256)</text>
<text text-anchor="start" x="2474.76" y="-518.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getUnderlyingPrice(pToken: IPToken): uint256</text>
<text text-anchor="start" x="2474.76" y="-502.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getPrice(asset: address): uint256</text>
<text text-anchor="start" x="2474.76" y="-485.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configs(asset: address): AssetConfig</text>
<text text-anchor="start" x="2474.76" y="-468.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2474.76" y="-451.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AssetConfigSet(asset: address, mainOracle: address, fallbackOracle: address, lowerBoundRatio: uint256, upperBoundRatio: uint256)</text>
</g>
<!-- 29&#45;&gt;26 -->
<g id="edge43" class="edge">
<title>29&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2945.03,-627.59C2971.59,-670.23 3005.73,-725.04 3043.61,-785.86"/>
<polygon fill="black" stroke="black" points="3040.72,-787.84 3048.98,-794.48 3046.66,-784.14 3040.72,-787.84"/>
</g>
<!-- 30 -->
<g id="node31" class="node">
<title>30</title>
<polygon fill="#f2f2f2" stroke="black" points="2773,-1348.9 2773,-1482.5 3003.59,-1482.5 3003.59,-1348.9 2773,-1348.9"/>
<text text-anchor="middle" x="2888.29" y="-1465.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2888.29" y="-1449.1" font-family="Times,serif" font-size="14.00">AssetConfig</text>
<text text-anchor="middle" x="2888.29" y="-1432.3" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2773,-1424.1 3003.59,-1424.1 "/>
<text text-anchor="start" x="2781" y="-1407.5" font-family="Times,serif" font-size="14.00">mainOracle: address</text>
<text text-anchor="start" x="2781" y="-1390.7" font-family="Times,serif" font-size="14.00">fallbackOracle: address</text>
<text text-anchor="start" x="2781" y="-1373.9" font-family="Times,serif" font-size="14.00">lowerBoundRatio: uint256</text>
<text text-anchor="start" x="2781" y="-1357.1" font-family="Times,serif" font-size="14.00">upperBoundRatio: uint256</text>
</g>
<!-- 29&#45;&gt;30 -->
<g id="edge44" class="edge">
<title>29&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2883.98,-627.59C2880.63,-801.26 2880.73,-1176.81 2884.28,-1338.32"/>
<polygon fill="black" stroke="black" points="2880.79,-1338.73 2884.52,-1348.65 2887.79,-1338.57 2880.79,-1338.73"/>
</g>
<!-- 30&#45;&gt;29 -->
<g id="edge42" class="edge">
<title>30&#45;&gt;29</title>
<path fill="none" stroke="black" d="M2892.07,-1348.65C2895.76,-1196.81 2896.02,-822.02 2892.83,-639.59"/>
<polygon fill="black" stroke="black" points="2892.83,-639.58 2888.72,-633.66 2892.61,-627.59 2896.72,-633.51 2892.83,-639.58"/>
</g>
<!-- 31 -->
<g id="node32" class="node">
<title>31</title>
<polygon fill="#f2f2f2" stroke="black" points="1854.42,-0.5 1854.42,-276.5 2716.16,-276.5 2716.16,-0.5 1854.42,-0.5"/>
<text text-anchor="middle" x="2285.29" y="-259.9" font-family="Times,serif" font-size="14.00">OracleEngine</text>
<text text-anchor="middle" x="2285.29" y="-243.1" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="1854.42,-234.9 2716.16,-234.9 "/>
<text text-anchor="start" x="1862.42" y="-218.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1862.42" y="-201.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CONFIGURATOR_PERMISSION: bytes32</text>
<text text-anchor="start" x="1862.42" y="-184.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_ORACLE_ENGINE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="1854.42,-176.5 2716.16,-176.5 "/>
<text text-anchor="start" x="1862.42" y="-159.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1862.42" y="-143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOracleEngineStorage(): (data: OracleEngineStorage)</text>
<text text-anchor="start" x="1862.42" y="-126.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1862.42" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setAssetConfig(asset: address, mainOracle: address, fallbackOracle: address, lowerBoundRatio: uint256, upperBoundRatio: uint256) &lt;&lt;onlyRole&gt;&gt;</text>
<text text-anchor="start" x="1862.42" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getUnderlyingPrice(pToken: IPToken): uint256</text>
<text text-anchor="start" x="1862.42" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;configs(asset: address): AssetConfig</text>
<text text-anchor="start" x="1862.42" y="-59.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1862.42" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor()</text>
<text text-anchor="start" x="1862.42" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;initialize(owner: address, configurator: address) &lt;&lt;initializer&gt;&gt;</text>
<text text-anchor="start" x="1862.42" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getPrice(asset: address): (price: uint256)</text>
</g>
<!-- 31&#45;&gt;22 -->
<g id="edge47" class="edge">
<title>31&#45;&gt;22</title>
<path fill="none" stroke="black" d="M1854.35,-264.48C1614.11,-334.15 1325.66,-417.81 1134.13,-473.35"/>
<polygon fill="none" stroke="black" points="1131.01,-463.33 1105.12,-481.77 1136.86,-483.49 1131.01,-463.33"/>
</g>
<!-- 31&#45;&gt;26 -->
<g id="edge49" class="edge">
<title>31&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2716.42,-275.33C2719.38,-275.9 2722.34,-276.46 2725.29,-277 2790.33,-288.95 3271.51,-267.29 3319.29,-313 3446.17,-434.36 3497.3,-609.27 3510.59,-784.1"/>
<polygon fill="black" stroke="black" points="3507.11,-784.55 3511.32,-794.28 3514.09,-784.05 3507.11,-784.55"/>
</g>
<!-- 31&#45;&gt;29 -->
<g id="edge46" class="edge">
<title>31&#45;&gt;29</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2494.5,-276.54C2569.48,-325.66 2652.89,-380.3 2723.57,-426.6"/>
<polygon fill="none" stroke="black" points="2718.15,-435.6 2749,-443.25 2729.66,-418.03 2718.15,-435.6"/>
</g>
<!-- 31&#45;&gt;30 -->
<g id="edge48" class="edge">
<title>31&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2401.8,-276.66C2408.53,-288.5 2414.49,-300.68 2419.29,-313 2455.31,-405.48 2388.24,-686.71 2457.29,-758 2552.57,-856.36 2662.59,-701.2 2763.29,-794 2916.13,-934.85 2911.39,-1206.23 2898.47,-1338.49"/>
<polygon fill="black" stroke="black" points="2894.98,-1338.24 2897.44,-1348.54 2901.94,-1338.95 2894.98,-1338.24"/>
</g>
<!-- 32 -->
<g id="node33" class="node">
<title>32</title>
<polygon fill="#f2f2f2" stroke="black" points="2159.94,-493.9 2159.94,-577.1 2410.64,-577.1 2410.64,-493.9 2159.94,-493.9"/>
<text text-anchor="middle" x="2285.29" y="-560.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="2285.29" y="-543.7" font-family="Times,serif" font-size="14.00">OracleEngineStorage</text>
<text text-anchor="middle" x="2285.29" y="-526.9" font-family="Times,serif" font-size="14.00">public/flatten/OracleEngine.flatten.sol</text>
<polyline fill="none" stroke="black" points="2159.94,-518.7 2410.64,-518.7 "/>
<text text-anchor="start" x="2167.94" y="-502.1" font-family="Times,serif" font-size="14.00">configs: mapping(address=&gt;AssetConfig)</text>
</g>
<!-- 31&#45;&gt;32 -->
<g id="edge50" class="edge">
<title>31&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2278.83,-276.54C2278.13,-348.19 2278.71,-431.6 2280.59,-483.72"/>
<polygon fill="black" stroke="black" points="2277.1,-483.87 2280.99,-493.72 2284.09,-483.59 2277.1,-483.87"/>
</g>
<!-- 32&#45;&gt;30 -->
<g id="edge51" class="edge">
<title>32&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2298.32,-577.24C2317.15,-628.87 2357.28,-716.17 2424.29,-758 2488.56,-798.12 2707.29,-742.98 2763.29,-794 2917.12,-934.16 2911.79,-1206.47 2898.58,-1338.83"/>
<polygon fill="black" stroke="black" points="2895.09,-1338.57 2897.53,-1348.88 2902.05,-1339.3 2895.09,-1338.57"/>
</g>
<!-- 32&#45;&gt;31 -->
<g id="edge45" class="edge">
<title>32&#45;&gt;31</title>
<path fill="none" stroke="black" d="M2289.6,-493.72C2291.67,-445.45 2292.43,-362.33 2291.86,-288.74"/>
<polygon fill="black" stroke="black" points="2291.86,-288.54 2287.81,-282.58 2291.75,-276.54 2295.81,-282.51 2291.86,-288.54"/>
</g>
</g>
</svg>
