<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="956pt" height="727pt"
 viewBox="0.00 0.00 955.73 727.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 723)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-723 951.73,-723 951.73,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="79.01,-71.7 79.01,-130.1 326.73,-130.1 326.73,-71.7 79.01,-71.7"/>
<text text-anchor="middle" x="202.87" y="-113.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="202.87" y="-96.7" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="202.87" y="-79.9" font-family="Times,serif" font-size="14.00">public/flatten/OwnableModule.flatten.sol</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-238.3 0,-489.5 415.75,-489.5 415.75,-238.3 0,-238.3"/>
<text text-anchor="middle" x="207.87" y="-472.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="207.87" y="-456.1" font-family="Times,serif" font-size="14.00">IOwnable</text>
<text text-anchor="middle" x="207.87" y="-439.3" font-family="Times,serif" font-size="14.00">public/flatten/OwnableModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-431.1 415.75,-431.1 "/>
<text text-anchor="start" x="8" y="-414.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="8" y="-397.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;acceptOwnership()</text>
<text text-anchor="start" x="8" y="-380.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferOwnership(newOwner: address)</text>
<text text-anchor="start" x="8" y="-364.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nominateNewOwner(newNominatedOwner: address)</text>
<text text-anchor="start" x="8" y="-347.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceNomination()</text>
<text text-anchor="start" x="8" y="-330.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceOwnership()</text>
<text text-anchor="start" x="8" y="-313.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="8" y="-296.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;pendingOwner(): address</text>
<text text-anchor="start" x="8" y="-280.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="8" y="-263.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnerNominated(newOwner: address)</text>
<text text-anchor="start" x="8" y="-246.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnerChanged(oldOwner: address, newOwner: address)</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="534.36,-526.5 534.36,-718.5 847.39,-718.5 847.39,-526.5 534.36,-526.5"/>
<text text-anchor="middle" x="690.87" y="-701.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="690.87" y="-685.1" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="690.87" y="-668.3" font-family="Times,serif" font-size="14.00">public/flatten/OwnableModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="534.36,-660.1 847.39,-660.1 "/>
<text text-anchor="start" x="542.36" y="-643.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="542.36" y="-626.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="534.36,-618.5 847.39,-618.5 "/>
<text text-anchor="start" x="542.36" y="-601.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="542.36" y="-585.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="542.36" y="-568.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="542.36" y="-551.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="542.36" y="-534.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="434.01,-313.9 434.01,-413.9 681.73,-413.9 681.73,-313.9 434.01,-313.9"/>
<text text-anchor="middle" x="557.87" y="-397.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="557.87" y="-380.5" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="557.87" y="-363.7" font-family="Times,serif" font-size="14.00">public/flatten/OwnableModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="434.01,-355.5 681.73,-355.5 "/>
<text text-anchor="start" x="442.01" y="-338.9" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="442.01" y="-322.1" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 2&#45;&gt;3 -->
<g id="edge2" class="edge">
<title>2&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M648.05,-526.24C630.49,-491.54 610.44,-453.37 593.67,-422.87"/>
<polygon fill="black" stroke="black" points="596.71,-421.13 588.81,-414.07 590.58,-424.52 596.71,-421.13"/>
</g>
<!-- 3&#45;&gt;2 -->
<g id="edge1" class="edge">
<title>3&#45;&gt;2</title>
<path fill="none" stroke="black" d="M577.91,-414.07C591.72,-442.82 610.72,-480.27 629.23,-515.42"/>
<polygon fill="black" stroke="black" points="629.34,-515.64 635.68,-519.07 634.95,-526.24 628.61,-522.81 629.34,-515.64"/>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="700.01,-322.3 700.01,-405.5 947.73,-405.5 947.73,-322.3 700.01,-322.3"/>
<text text-anchor="middle" x="823.87" y="-388.9" font-family="Times,serif" font-size="14.00">OwnableMixin</text>
<text text-anchor="middle" x="823.87" y="-372.1" font-family="Times,serif" font-size="14.00">public/flatten/OwnableModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="700.01,-363.9 947.73,-363.9 "/>
<text text-anchor="start" x="708.01" y="-347.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="708.01" y="-330.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
</g>
<!-- 4&#45;&gt;2 -->
<g id="edge3" class="edge">
<title>4&#45;&gt;2</title>
<path fill="none" stroke="black" d="M802.8,-405.56C789.53,-431.15 771.65,-465.65 754.08,-499.56"/>
<polygon fill="none" stroke="black" points="744.62,-494.98 740.14,-526.45 763.27,-504.64 744.62,-494.98"/>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="344.36,-0.5 344.36,-201.3 771.38,-201.3 771.38,-0.5 344.36,-0.5"/>
<text text-anchor="middle" x="557.87" y="-184.7" font-family="Times,serif" font-size="14.00">OwnableModule</text>
<text text-anchor="middle" x="557.87" y="-167.9" font-family="Times,serif" font-size="14.00">public/flatten/OwnableModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="344.36,-159.7 771.38,-159.7 "/>
<text text-anchor="start" x="352.36" y="-143.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="352.36" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferOwnership(newOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="352.36" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceNomination()</text>
<text text-anchor="start" x="352.36" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceOwnership() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="352.36" y="-75.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="352.36" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;acceptOwnership()</text>
<text text-anchor="start" x="352.36" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;nominateNewOwner(newNominatedOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="352.36" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="352.36" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;pendingOwner(): address</text>
</g>
<!-- 5&#45;&gt;1 -->
<g id="edge4" class="edge">
<title>5&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M424.55,-201.32C416.36,-207.43 408.04,-213.63 399.67,-219.87"/>
<polygon fill="none" stroke="black" points="393.02,-211.73 375.25,-238.08 405.58,-228.57 393.02,-211.73"/>
</g>
<!-- 5&#45;&gt;3 -->
<g id="edge6" class="edge">
<title>5&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M557.87,-201.32C557.87,-235.72 557.87,-273.19 557.87,-303.38"/>
<polygon fill="black" stroke="black" points="554.37,-303.73 557.87,-313.73 561.37,-303.73 554.37,-303.73"/>
</g>
<!-- 5&#45;&gt;4 -->
<g id="edge5" class="edge">
<title>5&#45;&gt;4</title>
<path fill="none" stroke="black" d="M659.19,-201.32C693.24,-234.72 730.23,-271.01 760.51,-300.73"/>
<polygon fill="none" stroke="black" points="753.43,-308.49 782.19,-322.01 768.14,-293.5 753.43,-308.49"/>
</g>
</g>
</svg>
