<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="1316pt" height="973pt"
 viewBox="0.00 0.00 1316.18 972.80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 968.8)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-968.8 1312.18,-968.8 1312.18,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-137.5 0,-447.1 313.77,-447.1 313.77,-137.5 0,-137.5"/>
<text text-anchor="middle" x="156.89" y="-430.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="156.89" y="-413.7" font-family="Times,serif" font-size="14.00">Initializable</text>
<text text-anchor="middle" x="156.89" y="-396.9" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="0,-388.7 313.77,-388.7 "/>
<text text-anchor="start" x="8" y="-372.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="8" y="-355.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;INITIALIZABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="0,-347.1 313.77,-347.1 "/>
<text text-anchor="start" x="8" y="-330.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="8" y="-313.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializableStorage(): ($: InitializableStorage)</text>
<text text-anchor="start" x="8" y="-296.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="8" y="-280.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkInitializing()</text>
<text text-anchor="start" x="8" y="-263.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_disableInitializers()</text>
<text text-anchor="start" x="8" y="-246.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getInitializedVersion(): uint64</text>
<text text-anchor="start" x="8" y="-229.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isInitializing(): bool</text>
<text text-anchor="start" x="8" y="-212.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="8" y="-196.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Initialized(version: uint64)</text>
<text text-anchor="start" x="8" y="-179.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; initializer()</text>
<text text-anchor="start" x="8" y="-162.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; reinitializer(version: uint64)</text>
<text text-anchor="start" x="8" y="-145.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyInitializing()</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="20.59,-559.7 20.59,-659.7 293.18,-659.7 293.18,-559.7 20.59,-559.7"/>
<text text-anchor="middle" x="156.89" y="-643.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="156.89" y="-626.3" font-family="Times,serif" font-size="14.00">InitializableStorage</text>
<text text-anchor="middle" x="156.89" y="-609.5" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="20.59,-601.3 293.18,-601.3 "/>
<text text-anchor="start" x="28.59" y="-584.7" font-family="Times,serif" font-size="14.00">_initialized: uint64</text>
<text text-anchor="start" x="28.59" y="-567.9" font-family="Times,serif" font-size="14.00">_initializing: bool</text>
</g>
<!-- 0&#45;&gt;1 -->
<g id="edge2" class="edge">
<title>0&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M150.14,-447.3C150.11,-483.51 150.55,-519.98 151.47,-549.07"/>
<polygon fill="black" stroke="black" points="147.98,-549.57 151.83,-559.44 154.98,-549.33 147.98,-549.57"/>
</g>
<!-- 1&#45;&gt;0 -->
<g id="edge1" class="edge">
<title>1&#45;&gt;0</title>
<path fill="none" stroke="black" d="M161.95,-559.44C162.99,-532.04 163.55,-496.18 163.63,-459.69"/>
<polygon fill="black" stroke="black" points="163.63,-459.3 159.63,-453.3 163.64,-447.3 167.63,-453.31 163.63,-459.3"/>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="517.59,-21.3 517.59,-79.7 790.18,-79.7 790.18,-21.3 517.59,-21.3"/>
<text text-anchor="middle" x="653.89" y="-63.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="653.89" y="-46.3" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="653.89" y="-29.5" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="311.01,-484.1 311.01,-735.3 726.76,-735.3 726.76,-484.1 311.01,-484.1"/>
<text text-anchor="middle" x="518.89" y="-718.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="518.89" y="-701.9" font-family="Times,serif" font-size="14.00">IOwnable</text>
<text text-anchor="middle" x="518.89" y="-685.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="311.01,-676.9 726.76,-676.9 "/>
<text text-anchor="start" x="319.01" y="-660.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="319.01" y="-643.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;acceptOwnership()</text>
<text text-anchor="start" x="319.01" y="-626.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferOwnership(newOwner: address)</text>
<text text-anchor="start" x="319.01" y="-609.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nominateNewOwner(newNominatedOwner: address)</text>
<text text-anchor="start" x="319.01" y="-593.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceNomination()</text>
<text text-anchor="start" x="319.01" y="-576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;renounceOwnership()</text>
<text text-anchor="start" x="319.01" y="-559.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="319.01" y="-542.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;pendingOwner(): address</text>
<text text-anchor="start" x="319.01" y="-525.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="319.01" y="-509.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnerNominated(newOwner: address)</text>
<text text-anchor="start" x="319.01" y="-492.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnerChanged(oldOwner: address, newOwner: address)</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="869.37,-772.3 869.37,-964.3 1182.4,-964.3 1182.4,-772.3 869.37,-772.3"/>
<text text-anchor="middle" x="1025.89" y="-947.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1025.89" y="-930.9" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="1025.89" y="-914.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="869.37,-905.9 1182.4,-905.9 "/>
<text text-anchor="start" x="877.37" y="-889.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="877.37" y="-872.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="869.37,-864.3 1182.4,-864.3 "/>
<text text-anchor="start" x="877.37" y="-847.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="877.37" y="-830.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="877.37" y="-814.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="877.37" y="-797.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="877.37" y="-780.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="744.59,-559.7 744.59,-659.7 1017.18,-659.7 1017.18,-559.7 744.59,-559.7"/>
<text text-anchor="middle" x="880.89" y="-643.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="880.89" y="-626.3" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="880.89" y="-609.5" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="744.59,-601.3 1017.18,-601.3 "/>
<text text-anchor="start" x="752.59" y="-584.7" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="752.59" y="-567.9" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 4&#45;&gt;5 -->
<g id="edge4" class="edge">
<title>4&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M978.61,-772.04C959.43,-737.34 937.59,-699.17 919.4,-668.67"/>
<polygon fill="black" stroke="black" points="922.26,-666.65 914.12,-659.87 916.26,-670.25 922.26,-666.65"/>
</g>
<!-- 5&#45;&gt;4 -->
<g id="edge3" class="edge">
<title>5&#45;&gt;4</title>
<path fill="none" stroke="black" d="M903.23,-659.87C918.37,-688.62 939.13,-726.07 959.28,-761.22"/>
<polygon fill="black" stroke="black" points="959.53,-761.64 965.99,-764.85 965.51,-772.04 959.05,-768.84 959.53,-761.64"/>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="1035.59,-568.1 1035.59,-651.3 1308.18,-651.3 1308.18,-568.1 1035.59,-568.1"/>
<text text-anchor="middle" x="1171.89" y="-634.7" font-family="Times,serif" font-size="14.00">OwnableMixin</text>
<text text-anchor="middle" x="1171.89" y="-617.9" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="1035.59,-609.7 1308.18,-609.7 "/>
<text text-anchor="start" x="1043.59" y="-593.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1043.59" y="-576.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
</g>
<!-- 6&#45;&gt;4 -->
<g id="edge5" class="edge">
<title>6&#45;&gt;4</title>
<path fill="none" stroke="black" d="M1148.75,-651.36C1134.08,-677.14 1114.27,-711.97 1094.84,-746.12"/>
<polygon fill="none" stroke="black" points="1085.68,-740.98 1079.97,-772.25 1103.93,-751.36 1085.68,-740.98"/>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="331.37,-191.9 331.37,-392.7 758.4,-392.7 758.4,-191.9 331.37,-191.9"/>
<text text-anchor="middle" x="544.89" y="-376.1" font-family="Times,serif" font-size="14.00">OwnableModule</text>
<text text-anchor="middle" x="544.89" y="-359.3" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="331.37,-351.1 758.4,-351.1 "/>
<text text-anchor="start" x="339.37" y="-334.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="339.37" y="-317.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferOwnership(newOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="339.37" y="-300.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceNomination()</text>
<text text-anchor="start" x="339.37" y="-284.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceOwnership() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="339.37" y="-267.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="339.37" y="-250.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;acceptOwnership()</text>
<text text-anchor="start" x="339.37" y="-233.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;nominateNewOwner(newNominatedOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="339.37" y="-216.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="339.37" y="-200.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;pendingOwner(): address</text>
</g>
<!-- 7&#45;&gt;3 -->
<g id="edge6" class="edge">
<title>7&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M536.66,-393.09C535.06,-412.5 533.35,-433.25 531.64,-453.95"/>
<polygon fill="none" stroke="black" points="521.16,-453.28 529.16,-484.05 542.09,-455.01 521.16,-453.28"/>
</g>
<!-- 7&#45;&gt;5 -->
<g id="edge8" class="edge">
<title>7&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M651.2,-393.09C707.23,-445.69 773.88,-508.26 820.92,-552.41"/>
<polygon fill="black" stroke="black" points="818.61,-555.04 828.29,-559.33 823.4,-549.94 818.61,-555.04"/>
</g>
<!-- 7&#45;&gt;6 -->
<g id="edge7" class="edge">
<title>7&#45;&gt;6</title>
<path fill="none" stroke="black" d="M758.61,-360.57C844.7,-392.02 943.02,-433.63 1025.89,-483.6 1055.56,-501.49 1085.18,-525.45 1109.91,-547.61"/>
<polygon fill="none" stroke="black" points="1102.82,-555.35 1131.96,-568.04 1117.09,-539.95 1102.82,-555.35"/>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="202.31,-0.5 202.31,-100.5 499.47,-100.5 499.47,-0.5 202.31,-0.5"/>
<text text-anchor="middle" x="350.89" y="-83.9" font-family="Times,serif" font-size="14.00">InitialModuleBeacon</text>
<text text-anchor="middle" x="350.89" y="-67.1" font-family="Times,serif" font-size="14.00">public/flatten/InitialModuleBeacon.flatten.sol</text>
<polyline fill="none" stroke="black" points="202.31,-58.9 499.47,-58.9 "/>
<text text-anchor="start" x="210.31" y="-42.3" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="210.31" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor()</text>
<text text-anchor="start" x="210.31" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;initialize(initialOwner: address) &lt;&lt;initializer&gt;&gt;</text>
</g>
<!-- 8&#45;&gt;0 -->
<g id="edge10" class="edge">
<title>8&#45;&gt;0</title>
<path fill="none" stroke="black" d="M311.16,-100.61C307.68,-104.91 304.05,-109.4 300.29,-114.04"/>
<polygon fill="none" stroke="black" points="292.03,-107.56 281.32,-137.49 308.35,-120.77 292.03,-107.56"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge9" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" d="M390.61,-100.61C406.66,-120.44 425.96,-144.3 445.4,-168.33"/>
<polygon fill="none" stroke="black" points="437.34,-175.05 464.37,-191.77 453.66,-161.85 437.34,-175.05"/>
</g>
</g>
</svg>
