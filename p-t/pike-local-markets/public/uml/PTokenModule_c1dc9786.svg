<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="6487pt" height="5400pt"
 viewBox="0.00 0.00 6487.24 5399.80" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 5395.8)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-5395.8 6483.24,-5395.8 6483.24,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="523.68,-3590.3 523.68,-3648.7 764.42,-3648.7 764.42,-3590.3 523.68,-3590.3"/>
<text text-anchor="middle" x="644.05" y="-3632.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="644.05" y="-3615.3" font-family="Times,serif" font-size="14.00">IERC20Errors</text>
<text text-anchor="middle" x="644.05" y="-3598.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="4378.68,-760.5 4378.68,-818.9 4619.42,-818.9 4619.42,-760.5 4378.68,-760.5"/>
<text text-anchor="middle" x="4499.05" y="-802.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4499.05" y="-785.5" font-family="Times,serif" font-size="14.00">IERC721Errors</text>
<text text-anchor="middle" x="4499.05" y="-768.7" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="4637.68,-760.5 4637.68,-818.9 4878.42,-818.9 4878.42,-760.5 4637.68,-760.5"/>
<text text-anchor="middle" x="4758.05" y="-802.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4758.05" y="-785.5" font-family="Times,serif" font-size="14.00">IERC1155Errors</text>
<text text-anchor="middle" x="4758.05" y="-768.7" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="1084.37,-4302.9 1084.37,-4537.3 1513.73,-4537.3 1513.73,-4302.9 1084.37,-4302.9"/>
<text text-anchor="middle" x="1299.05" y="-4520.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1299.05" y="-4503.9" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="1299.05" y="-4487.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1084.37,-4478.9 1513.73,-4478.9 "/>
<text text-anchor="start" x="1092.37" y="-4462.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1092.37" y="-4445.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="1092.37" y="-4428.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="1092.37" y="-4411.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="1092.37" y="-4395.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="1092.37" y="-4378.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="1092.37" y="-4361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
<text text-anchor="start" x="1092.37" y="-4344.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1092.37" y="-4327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1092.37" y="-4311.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="4896.11,-722.9 4896.11,-856.5 5527.99,-856.5 5527.99,-722.9 4896.11,-722.9"/>
<text text-anchor="middle" x="5212.05" y="-839.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="5212.05" y="-823.1" font-family="Times,serif" font-size="14.00">IERC20Permit</text>
<text text-anchor="middle" x="5212.05" y="-806.3" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4896.11,-798.1 5527.99,-798.1 "/>
<text text-anchor="start" x="4904.11" y="-781.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="4904.11" y="-764.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;permit(owner: address, spender: address, value: uint256, deadline: uint256, v: uint8, r: bytes32, s: bytes32)</text>
<text text-anchor="start" x="4904.11" y="-747.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;nonces(owner: address): uint256</text>
<text text-anchor="start" x="4904.11" y="-731.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;DOMAIN_SEPARATOR(): bytes32</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="1851.61,-3502.3 1851.61,-3736.7 2350.48,-3736.7 2350.48,-3502.3 1851.61,-3502.3"/>
<text text-anchor="middle" x="2101.05" y="-3720.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2101.05" y="-3703.3" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="2101.05" y="-3686.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1851.61,-3678.3 2350.48,-3678.3 "/>
<text text-anchor="start" x="1859.61" y="-3661.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1859.61" y="-3644.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_revert(returndata: bytes)</text>
<text text-anchor="start" x="1859.61" y="-3628.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1859.61" y="-3611.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="1859.61" y="-3594.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1859.61" y="-3577.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="1859.61" y="-3560.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1859.61" y="-3544.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionDelegateCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="1859.61" y="-3527.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResultFromTarget(target: address, success: bool, returndata: bytes): bytes</text>
<text text-anchor="start" x="1859.61" y="-3510.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes): bytes</text>
</g>
<!-- 6 -->
<g id="node7" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="782.68,-3552.7 782.68,-3686.3 1023.42,-3686.3 1023.42,-3552.7 782.68,-3552.7"/>
<text text-anchor="middle" x="903.05" y="-3669.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="903.05" y="-3652.9" font-family="Times,serif" font-size="14.00">Context</text>
<text text-anchor="middle" x="903.05" y="-3636.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="782.68,-3627.9 1023.42,-3627.9 "/>
<text text-anchor="start" x="790.68" y="-3611.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="790.68" y="-3594.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="790.68" y="-3577.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
<text text-anchor="start" x="790.68" y="-3560.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_contextSuffixLength(): uint256</text>
</g>
<!-- 7 -->
<g id="node8" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="100.2,-2932.9 100.2,-3352.1 601.9,-3352.1 601.9,-2932.9 100.2,-2932.9"/>
<text text-anchor="middle" x="351.05" y="-3335.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="351.05" y="-3318.7" font-family="Times,serif" font-size="14.00">Math</text>
<text text-anchor="middle" x="351.05" y="-3301.9" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="100.2,-3293.7 601.9,-3293.7 "/>
<text text-anchor="start" x="108.2" y="-3277.1" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="108.2" y="-3260.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryAdd(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="108.2" y="-3243.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;trySub(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="108.2" y="-3226.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMul(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="108.2" y="-3209.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryDiv(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="108.2" y="-3193.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;tryMod(a: uint256, b: uint256): (bool, uint256)</text>
<text text-anchor="start" x="108.2" y="-3176.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;max(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-3159.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;min(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-3142.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;average(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-3125.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;ceilDiv(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-3109.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256): (result: uint256)</text>
<text text-anchor="start" x="108.2" y="-3092.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mulDiv(x: uint256, y: uint256, denominator: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="108.2" y="-3075.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-3058.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sqrt(a: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="108.2" y="-3041.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-3025.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log2(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="108.2" y="-3008.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-2991.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log10(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="108.2" y="-2974.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256): uint256</text>
<text text-anchor="start" x="108.2" y="-2957.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;log256(value: uint256, rounding: Rounding): uint256</text>
<text text-anchor="start" x="108.2" y="-2941.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;unsignedRoundsUp(rounding: Rounding): bool</text>
</g>
<!-- 8 -->
<g id="node9" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="230.68,-3552.7 230.68,-3686.3 471.42,-3686.3 471.42,-3552.7 230.68,-3552.7"/>
<text text-anchor="middle" x="351.05" y="-3669.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="351.05" y="-3652.9" font-family="Times,serif" font-size="14.00">Rounding</text>
<text text-anchor="middle" x="351.05" y="-3636.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="230.68,-3627.9 471.42,-3627.9 "/>
<text text-anchor="start" x="238.68" y="-3611.3" font-family="Times,serif" font-size="14.00">Floor: 0</text>
<text text-anchor="start" x="238.68" y="-3594.5" font-family="Times,serif" font-size="14.00">Ceil: 1</text>
<text text-anchor="start" x="238.68" y="-3577.7" font-family="Times,serif" font-size="14.00">Trunc: 2</text>
<text text-anchor="start" x="238.68" y="-3560.9" font-family="Times,serif" font-size="14.00">Expand: 3</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge2" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M344.34,-3352.19C344.13,-3419.91 344.67,-3490.8 345.94,-3542.4"/>
<polygon fill="black" stroke="black" points="342.44,-3542.55 346.2,-3552.46 349.44,-3542.37 342.44,-3542.55"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge1" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" d="M355.89,-3552.46C357.28,-3503.22 357.91,-3432.94 357.79,-3364.49"/>
<polygon fill="black" stroke="black" points="357.78,-3364.19 353.77,-3358.2 357.76,-3352.19 361.77,-3358.18 357.78,-3364.19"/>
</g>
<!-- 9 -->
<g id="node10" class="node">
<title>9</title>
<polygon fill="#f2f2f2" stroke="black" points="1546.68,-2207.9 1546.68,-2266.3 1787.42,-2266.3 1787.42,-2207.9 1546.68,-2207.9"/>
<text text-anchor="middle" x="1667.05" y="-2249.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1667.05" y="-2232.9" font-family="Times,serif" font-size="14.00">CommonError</text>
<text text-anchor="middle" x="1667.05" y="-2216.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
</g>
<!-- 10 -->
<g id="node11" class="node">
<title>10</title>
<polygon fill="#f2f2f2" stroke="black" points="1805.68,-2207.9 1805.68,-2266.3 2046.42,-2266.3 2046.42,-2207.9 1805.68,-2207.9"/>
<text text-anchor="middle" x="1926.05" y="-2249.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="1926.05" y="-2232.9" font-family="Times,serif" font-size="14.00">PTokenError</text>
<text text-anchor="middle" x="1926.05" y="-2216.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
</g>
<!-- 11 -->
<g id="node12" class="node">
<title>11</title>
<polygon fill="#f2f2f2" stroke="black" points="3953.68,-5332.9 3953.68,-5391.3 4194.42,-5391.3 4194.42,-5332.9 3953.68,-5332.9"/>
<text text-anchor="middle" x="4074.05" y="-5374.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="4074.05" y="-5357.9" font-family="Times,serif" font-size="14.00">RiskEngineError</text>
<text text-anchor="middle" x="4074.05" y="-5341.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
</g>
<!-- 12 -->
<g id="node13" class="node">
<title>12</title>
<polygon fill="#f2f2f2" stroke="black" points="3944.54,-5027.9 3944.54,-5295.9 4203.56,-5295.9 4203.56,-5027.9 3944.54,-5027.9"/>
<text text-anchor="middle" x="4074.05" y="-5279.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="4074.05" y="-5262.5" font-family="Times,serif" font-size="14.00">Error</text>
<text text-anchor="middle" x="4074.05" y="-5245.7" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3944.54,-5237.5 4203.56,-5237.5 "/>
<text text-anchor="start" x="3952.54" y="-5220.9" font-family="Times,serif" font-size="14.00">NO_ERROR: 0</text>
<text text-anchor="start" x="3952.54" y="-5204.1" font-family="Times,serif" font-size="14.00">RISKENGINE_MISMATCH: 1</text>
<text text-anchor="start" x="3952.54" y="-5187.3" font-family="Times,serif" font-size="14.00">INSUFFICIENT_SHORTFALL: 2</text>
<text text-anchor="start" x="3952.54" y="-5170.5" font-family="Times,serif" font-size="14.00">INSUFFICIENT_LIQUIDITY: 3</text>
<text text-anchor="start" x="3952.54" y="-5153.7" font-family="Times,serif" font-size="14.00">MARKET_NOT_LISTED: 4</text>
<text text-anchor="start" x="3952.54" y="-5136.9" font-family="Times,serif" font-size="14.00">PRICE_ERROR: 5</text>
<text text-anchor="start" x="3952.54" y="-5120.1" font-family="Times,serif" font-size="14.00">TOO_MUCH_REPAY: 6</text>
<text text-anchor="start" x="3952.54" y="-5103.3" font-family="Times,serif" font-size="14.00">SUPPLY_CAP_EXCEEDED: 7</text>
<text text-anchor="start" x="3952.54" y="-5086.5" font-family="Times,serif" font-size="14.00">BORROW_CAP_EXCEEDED: 8</text>
<text text-anchor="start" x="3952.54" y="-5069.7" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_AS_COLLATERAL: 9</text>
<text text-anchor="start" x="3952.54" y="-5052.9" font-family="Times,serif" font-size="14.00">NOT_ALLOWED_TO_BORROW: 10</text>
<text text-anchor="start" x="3952.54" y="-5036.1" font-family="Times,serif" font-size="14.00">EMODE_NOT_ALLOWED: 11</text>
</g>
<!-- 12&#45;&gt;11 -->
<g id="edge3" class="edge">
<title>12&#45;&gt;11</title>
<path fill="none" stroke="black" d="M4074.05,-5295.93C4074.05,-5304.66 4074.05,-5312.99 4074.05,-5320.61"/>
<polygon fill="black" stroke="black" points="4074.05,-5320.68 4078.05,-5326.68 4074.05,-5332.68 4070.05,-5326.68 4074.05,-5320.68"/>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#f2f2f2" stroke="black" points="3634.82,-2170.3 3634.82,-2303.9 4275.28,-2303.9 4275.28,-2170.3 3634.82,-2170.3"/>
<text text-anchor="middle" x="3955.05" y="-2287.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3955.05" y="-2270.5" font-family="Times,serif" font-size="14.00">IInterestRateModel</text>
<text text-anchor="middle" x="3955.05" y="-2253.7" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3634.82,-2245.5 4275.28,-2245.5 "/>
<text text-anchor="start" x="3642.82" y="-2228.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3642.82" y="-2212.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getUtilization(cash: uint256, borrows: uint256, reserves: uint256): uint256</text>
<text text-anchor="start" x="3642.82" y="-2195.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getBorrowRate(cash: uint256, borrows: uint256, reserves: uint256): uint256</text>
<text text-anchor="start" x="3642.82" y="-2178.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getSupplyRate(cash: uint256, borrows: uint256, reserves: uint256, reserveFactorMantissa: uint256): uint256</text>
</g>
<!-- 14 -->
<g id="node15" class="node">
<title>14</title>
<polygon fill="#f2f2f2" stroke="black" points="4292.93,-2103.1 4292.93,-2371.1 4887.17,-2371.1 4887.17,-2103.1 4292.93,-2103.1"/>
<text text-anchor="middle" x="4590.05" y="-2354.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="4590.05" y="-2337.7" font-family="Times,serif" font-size="14.00">IRBAC</text>
<text text-anchor="middle" x="4590.05" y="-2320.9" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4292.93,-2312.7 4887.17,-2312.7 "/>
<text text-anchor="start" x="4300.93" y="-2296.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="4300.93" y="-2279.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2262.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;grantNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2245.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokePermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2228.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;revokeNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2212.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasPermission(permission: bytes32, target: address): bool</text>
<text text-anchor="start" x="4300.93" y="-2195.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;hasNestedPermission(permission: bytes32, nestedAddress: address, target: address): bool</text>
<text text-anchor="start" x="4300.93" y="-2178.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="4300.93" y="-2161.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PermissionGranted(permission: bytes32, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2144.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NestedPermissionGranted(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2128.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; PermissionRevoked(permission: bytes32, target: address)</text>
<text text-anchor="start" x="4300.93" y="-2111.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NestedPermissionRevoked(permission: bytes32, nestedAddress: address, target: address)</text>
</g>
<!-- 15 -->
<g id="node16" class="node">
<title>15</title>
<polygon fill="#f2f2f2" stroke="black" points="5962.54,-3046.5 5962.54,-3238.5 6275.56,-3238.5 6275.56,-3046.5 5962.54,-3046.5"/>
<text text-anchor="middle" x="6119.05" y="-3221.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="6119.05" y="-3205.1" font-family="Times,serif" font-size="14.00">OwnableStorage</text>
<text text-anchor="middle" x="6119.05" y="-3188.3" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="5962.54,-3180.1 6275.56,-3180.1 "/>
<text text-anchor="start" x="5970.54" y="-3163.5" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="5970.54" y="-3146.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_OWNABLE_STORAGE: bytes32</text>
<polyline fill="none" stroke="black" points="5962.54,-3138.5 6275.56,-3138.5 "/>
<text text-anchor="start" x="5970.54" y="-3121.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="5970.54" y="-3105.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="5970.54" y="-3088.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_owner(): address</text>
<text text-anchor="start" x="5970.54" y="-3071.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingOwner(): address</text>
<text text-anchor="start" x="5970.54" y="-3054.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getOwnableStorage(): ($: Ownable2StepStorage)</text>
</g>
<!-- 16 -->
<g id="node17" class="node">
<title>16</title>
<polygon fill="#f2f2f2" stroke="black" points="5998.68,-3569.5 5998.68,-3669.5 6239.42,-3669.5 6239.42,-3569.5 5998.68,-3569.5"/>
<text text-anchor="middle" x="6119.05" y="-3652.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="6119.05" y="-3636.1" font-family="Times,serif" font-size="14.00">Ownable2StepStorage</text>
<text text-anchor="middle" x="6119.05" y="-3619.3" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="5998.68,-3611.1 6239.42,-3611.1 "/>
<text text-anchor="start" x="6006.68" y="-3594.5" font-family="Times,serif" font-size="14.00">owner: address</text>
<text text-anchor="start" x="6006.68" y="-3577.7" font-family="Times,serif" font-size="14.00">pendingOwner: address</text>
</g>
<!-- 15&#45;&gt;16 -->
<g id="edge5" class="edge">
<title>15&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6113.52,-3238.77C6111.64,-3334.28 6111.93,-3478.38 6114.39,-3558.81"/>
<polygon fill="black" stroke="black" points="6110.91,-3559.37 6114.74,-3569.25 6117.9,-3559.14 6110.91,-3559.37"/>
</g>
<!-- 16&#45;&gt;15 -->
<g id="edge4" class="edge">
<title>16&#45;&gt;15</title>
<path fill="none" stroke="black" d="M6123.36,-3569.25C6126.03,-3494.53 6126.51,-3350.69 6124.81,-3251"/>
<polygon fill="black" stroke="black" points="6124.8,-3250.76 6120.69,-3244.84 6124.58,-3238.77 6128.69,-3244.69 6124.8,-3250.76"/>
</g>
<!-- 17 -->
<g id="node18" class="node">
<title>17</title>
<polygon fill="#f2f2f2" stroke="black" points="5461.69,-2057.1 5461.69,-2417.1 5980.41,-2417.1 5980.41,-2057.1 5461.69,-2057.1"/>
<text text-anchor="middle" x="5721.05" y="-2400.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="5721.05" y="-2383.7" font-family="Times,serif" font-size="14.00">RBACStorage</text>
<text text-anchor="middle" x="5721.05" y="-2366.9" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="5461.69,-2358.7 5980.41,-2358.7 "/>
<text text-anchor="start" x="5469.69" y="-2342.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="5469.69" y="-2325.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_RBAC_STORAGE: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2308.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="5469.69" y="-2291.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_CONFIGURATOR_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2274.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PROTOCOL_OWNER_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2258.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_OWNER_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2241.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_PAUSE_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2224.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_BORROW_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2207.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SUPPLY_CAP_GUARDIAN_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2190.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_MANAGER_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2174.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_WITHDRAWER_PERMISSION: bytes32</text>
<text text-anchor="start" x="5469.69" y="-2157.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_EMERGENCY_WITHDRAWER_PERMISSION: bytes32</text>
<polyline fill="none" stroke="black" points="5461.69,-2149.1 5980.41,-2149.1 "/>
<text text-anchor="start" x="5469.69" y="-2132.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="5469.69" y="-2115.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="5469.69" y="-2098.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkNestedPermission(permission: bytes32, nestedAddress: address, target: address)</text>
<text text-anchor="start" x="5469.69" y="-2082.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isPermissionValid(permission: bytes32)</text>
<text text-anchor="start" x="5469.69" y="-2065.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getRBACStorage(): ($: RBACData)</text>
</g>
<!-- 18 -->
<g id="node19" class="node">
<title>18</title>
<polygon fill="#f2f2f2" stroke="black" points="5443.26,-3092.5 5443.26,-3192.5 5944.83,-3192.5 5944.83,-3092.5 5443.26,-3092.5"/>
<text text-anchor="middle" x="5694.05" y="-3175.9" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="5694.05" y="-3159.1" font-family="Times,serif" font-size="14.00">RBACData</text>
<text text-anchor="middle" x="5694.05" y="-3142.3" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="5443.26,-3134.1 5944.83,-3134.1 "/>
<text text-anchor="start" x="5451.26" y="-3117.5" font-family="Times,serif" font-size="14.00">permissions: mapping(bytes32=&gt;mapping(address=&gt;bool))</text>
<text text-anchor="start" x="5451.26" y="-3100.7" font-family="Times,serif" font-size="14.00">nestedPermissions: mapping(bytes32=&gt;mapping(address=&gt;mapping(address=&gt;bool)))</text>
</g>
<!-- 17&#45;&gt;18 -->
<g id="edge7" class="edge">
<title>17&#45;&gt;18</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5710.19,-2417.12C5701.89,-2622.71 5692.8,-2948.74 5692.26,-3081.89"/>
<polygon fill="black" stroke="black" points="5688.76,-3082.27 5692.24,-3092.28 5695.76,-3082.29 5688.76,-3082.27"/>
</g>
<!-- 18&#45;&gt;17 -->
<g id="edge6" class="edge">
<title>18&#45;&gt;17</title>
<path fill="none" stroke="black" d="M5698.8,-3092.28C5706.12,-2968.88 5716.7,-2641.08 5720.97,-2429.32"/>
<polygon fill="black" stroke="black" points="5720.97,-2429.12 5717.09,-2423.04 5721.21,-2417.12 5725.09,-2423.2 5720.97,-2429.12"/>
</g>
<!-- 19 -->
<g id="node20" class="node">
<title>19</title>
<polygon fill="#f2f2f2" stroke="black" points="4942.83,-1939.5 4942.83,-2534.7 5443.27,-2534.7 5443.27,-1939.5 4942.83,-1939.5"/>
<text text-anchor="middle" x="5193.05" y="-2518.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="5193.05" y="-2501.3" font-family="Times,serif" font-size="14.00">ExponentialNoError</text>
<text text-anchor="middle" x="5193.05" y="-2484.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="4942.83,-2476.3 5443.27,-2476.3 "/>
<text text-anchor="start" x="4950.83" y="-2459.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="4950.83" y="-2442.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;expScale: uint256</text>
<text text-anchor="start" x="4950.83" y="-2426.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;doubleScale: uint256</text>
<polyline fill="none" stroke="black" points="4942.83,-2417.9 5443.27,-2417.9 "/>
<text text-anchor="start" x="4950.83" y="-2401.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="4950.83" y="-2384.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;toExp(value: uint256): Exp</text>
<text text-anchor="start" x="4950.83" y="-2367.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;toDouble(value: uint256): Double</text>
<text text-anchor="start" x="4950.83" y="-2350.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;truncate(value: Exp): uint256</text>
<text text-anchor="start" x="4950.83" y="-2334.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_ScalarTruncate(value: Exp, scalar: uint256): uint256</text>
<text text-anchor="start" x="4950.83" y="-2317.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_ScalarTruncateAddUInt(value: Exp, scalar: uint256, addend: uint256): uint256</text>
<text text-anchor="start" x="4950.83" y="-2300.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;lessThanExp(left: Exp, right: Exp): bool</text>
<text text-anchor="start" x="4950.83" y="-2283.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;lessThanOrEqualExp(left: Exp, right: Exp): bool</text>
<text text-anchor="start" x="4950.83" y="-2266.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;add_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4950.83" y="-2250.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;add_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4950.83" y="-2233.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;add_(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="4950.83" y="-2216.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sub_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4950.83" y="-2199.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sub_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4950.83" y="-2182.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sub_(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="4950.83" y="-2166.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4950.83" y="-2149.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Exp, b: uint256): Exp</text>
<text text-anchor="start" x="4950.83" y="-2132.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: uint256, b: Exp): uint256</text>
<text text-anchor="start" x="4950.83" y="-2115.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4950.83" y="-2098.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: Double, b: uint256): Double</text>
<text text-anchor="start" x="4950.83" y="-2082.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: uint256, b: Double): uint256</text>
<text text-anchor="start" x="4950.83" y="-2065.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mul_(a: uint256, b: uint256): uint256</text>
<text text-anchor="start" x="4950.83" y="-2048.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Exp, b: Exp): Exp</text>
<text text-anchor="start" x="4950.83" y="-2031.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Exp, b: uint256): Exp</text>
<text text-anchor="start" x="4950.83" y="-2014.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: uint256, b: Exp): uint256</text>
<text text-anchor="start" x="4950.83" y="-1998.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Double, b: Double): Double</text>
<text text-anchor="start" x="4950.83" y="-1981.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: Double, b: uint256): Double</text>
<text text-anchor="start" x="4950.83" y="-1964.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: uint256, b: Double): uint256</text>
<text text-anchor="start" x="4950.83" y="-1947.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;div_(a: uint256, b: uint256): uint256</text>
</g>
<!-- 20 -->
<g id="node21" class="node">
<title>20</title>
<polygon fill="#f2f2f2" stroke="black" points="1178.68,-3552.7 1178.68,-3686.3 1419.42,-3686.3 1419.42,-3552.7 1178.68,-3552.7"/>
<text text-anchor="middle" x="1299.05" y="-3669.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1299.05" y="-3652.9" font-family="Times,serif" font-size="14.00">IERC20Metadata</text>
<text text-anchor="middle" x="1299.05" y="-3636.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1178.68,-3627.9 1419.42,-3627.9 "/>
<text text-anchor="start" x="1186.68" y="-3611.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1186.68" y="-3594.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="1186.68" y="-3577.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="1186.68" y="-3560.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
</g>
<!-- 20&#45;&gt;3 -->
<g id="edge8" class="edge">
<title>20&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1299.05,-3686.53C1299.05,-3814.33 1299.05,-4098.21 1299.05,-4272.67"/>
<polygon fill="none" stroke="black" points="1288.55,-4272.85 1299.05,-4302.85 1309.55,-4272.85 1288.55,-4272.85"/>
</g>
<!-- 21 -->
<g id="node22" class="node">
<title>21</title>
<polygon fill="#f2f2f2" stroke="black" points="5998.68,-2195.5 5998.68,-2278.7 6239.42,-2278.7 6239.42,-2195.5 5998.68,-2195.5"/>
<text text-anchor="middle" x="6119.05" y="-2262.1" font-family="Times,serif" font-size="14.00">OwnableMixin</text>
<text text-anchor="middle" x="6119.05" y="-2245.3" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="5998.68,-2237.1 6239.42,-2237.1 "/>
<text text-anchor="start" x="6006.68" y="-2220.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="6006.68" y="-2203.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
</g>
<!-- 21&#45;&gt;15 -->
<g id="edge9" class="edge">
<title>21&#45;&gt;15</title>
<path fill="none" stroke="black" d="M6119.05,-2278.97C6119.05,-2407.9 6119.05,-2809.73 6119.05,-3016.22"/>
<polygon fill="none" stroke="black" points="6108.55,-3016.42 6119.05,-3046.42 6129.55,-3016.42 6108.55,-3016.42"/>
</g>
<!-- 22 -->
<g id="node23" class="node">
<title>22</title>
<polygon fill="#f2f2f2" stroke="black" points="1136.34,-2941.3 1136.34,-3343.7 1757.76,-3343.7 1757.76,-2941.3 1136.34,-2941.3"/>
<text text-anchor="middle" x="1447.05" y="-3327.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="1447.05" y="-3310.3" font-family="Times,serif" font-size="14.00">IERC4626</text>
<text text-anchor="middle" x="1447.05" y="-3293.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1136.34,-3285.3 1757.76,-3285.3 "/>
<text text-anchor="start" x="1144.34" y="-3268.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="1144.34" y="-3251.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): (assetTokenAddress: address)</text>
<text text-anchor="start" x="1144.34" y="-3235.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): (totalManagedAssets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3218.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3201.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3184.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(receiver: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3167.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3151.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(assets: uint256, receiver: address): (shares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3134.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3117.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3100.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(shares: uint256, receiver: address): (assets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3083.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): (maxAssets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3067.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3050.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): (shares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3033.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-3016.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-2999.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): (assets: uint256)</text>
<text text-anchor="start" x="1144.34" y="-2983.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1144.34" y="-2966.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Deposit(sender: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="1144.34" y="-2949.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Withdraw(sender: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
</g>
<!-- 22&#45;&gt;3 -->
<g id="edge10" class="edge">
<title>22&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1450.81,-3343.71C1451,-3477.07 1446.8,-3655.97 1428.05,-3812.8 1409.01,-3972.06 1368.41,-4151.38 1337.82,-4273.43"/>
<polygon fill="none" stroke="black" points="1327.57,-4271.13 1330.37,-4302.79 1347.92,-4276.29 1327.57,-4271.13"/>
</g>
<!-- 22&#45;&gt;20 -->
<g id="edge11" class="edge">
<title>22&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1384.61,-3343.91C1365.18,-3406.25 1344.67,-3472.08 1328.6,-3523.65"/>
<polygon fill="none" stroke="black" points="1318.48,-3520.85 1319.58,-3552.62 1338.53,-3527.1 1318.48,-3520.85"/>
</g>
<!-- 23 -->
<g id="node24" class="node">
<title>23</title>
<polygon fill="#f2f2f2" stroke="black" points="1813.95,-3033.7 1813.95,-3251.3 2334.15,-3251.3 2334.15,-3033.7 1813.95,-3033.7"/>
<text text-anchor="middle" x="2074.05" y="-3234.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="2074.05" y="-3217.9" font-family="Times,serif" font-size="14.00">SafeERC20</text>
<text text-anchor="middle" x="2074.05" y="-3201.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="1813.95,-3192.9 2334.15,-3192.9 "/>
<text text-anchor="start" x="1821.95" y="-3176.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1821.95" y="-3159.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturn(token: IERC20, data: bytes)</text>
<text text-anchor="start" x="1821.95" y="-3142.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturnBool(token: IERC20, data: bytes): bool</text>
<text text-anchor="start" x="1821.95" y="-3125.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1821.95" y="-3109.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransfer(token: IERC20, to: address, value: uint256)</text>
<text text-anchor="start" x="1821.95" y="-3092.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransferFrom(token: IERC20, from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="1821.95" y="-3075.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeIncreaseAllowance(token: IERC20, spender: address, value: uint256)</text>
<text text-anchor="start" x="1821.95" y="-3058.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeDecreaseAllowance(token: IERC20, spender: address, requestedDecrease: uint256)</text>
<text text-anchor="start" x="1821.95" y="-3041.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;forceApprove(token: IERC20, spender: address, value: uint256)</text>
</g>
<!-- 23&#45;&gt;3 -->
<g id="edge13" class="edge">
<title>23&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1961.01,-3251.42C1916.73,-3300.42 1870.13,-3361.78 1843.05,-3426.2 1776.06,-3585.57 1867.48,-3653.61 1800.05,-3812.8 1718.86,-4004.46 1556.73,-4182.84 1438.83,-4295.8"/>
<polygon fill="black" stroke="black" points="1436.31,-4293.37 1431.49,-4302.81 1441.14,-4298.44 1436.31,-4293.37"/>
</g>
<!-- 23&#45;&gt;5 -->
<g id="edge12" class="edge">
<title>23&#45;&gt;5</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2080.19,-3251.58C2084.25,-3322.96 2089.58,-3416.8 2093.84,-3491.7"/>
<polygon fill="black" stroke="black" points="2090.37,-3492.36 2094.44,-3502.14 2097.36,-3491.96 2090.37,-3492.36"/>
</g>
<!-- 24 -->
<g id="node25" class="node">
<title>24</title>
<polygon fill="#f2f2f2" stroke="black" points="620.05,-2895.3 620.05,-3389.7 1080.05,-3389.7 1080.05,-2895.3 620.05,-2895.3"/>
<text text-anchor="middle" x="850.05" y="-3373.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="850.05" y="-3356.3" font-family="Times,serif" font-size="14.00">ERC20</text>
<text text-anchor="middle" x="850.05" y="-3339.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="620.05,-3331.3 1080.05,-3331.3 "/>
<text text-anchor="start" x="628.05" y="-3314.7" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="628.05" y="-3297.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_balances: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="628.05" y="-3281.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_allowances: mapping(address=&gt;mapping(address=&gt;uint256))</text>
<text text-anchor="start" x="628.05" y="-3264.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_totalSupply: uint256</text>
<text text-anchor="start" x="628.05" y="-3247.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_name: string</text>
<text text-anchor="start" x="628.05" y="-3230.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_symbol: string</text>
<polyline fill="none" stroke="black" points="620.05,-3222.5 1080.05,-3222.5 "/>
<text text-anchor="start" x="628.05" y="-3205.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="628.05" y="-3189.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="628.05" y="-3172.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_update(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="628.05" y="-3155.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_mint(account: address, value: uint256)</text>
<text text-anchor="start" x="628.05" y="-3138.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_burn(account: address, value: uint256)</text>
<text text-anchor="start" x="628.05" y="-3121.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="628.05" y="-3105.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_approve(owner: address, spender: address, value: uint256, emitEvent: bool)</text>
<text text-anchor="start" x="628.05" y="-3088.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_spendAllowance(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="628.05" y="-3071.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="628.05" y="-3054.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(name_: string, symbol_: string)</text>
<text text-anchor="start" x="628.05" y="-3037.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="628.05" y="-3021.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="628.05" y="-3004.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="628.05" y="-2987.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="628.05" y="-2970.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="628.05" y="-2953.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transfer(to: address, value: uint256): bool</text>
<text text-anchor="start" x="628.05" y="-2937.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="628.05" y="-2920.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;approve(spender: address, value: uint256): bool</text>
<text text-anchor="start" x="628.05" y="-2903.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferFrom(from: address, to: address, value: uint256): bool</text>
</g>
<!-- 24&#45;&gt;0 -->
<g id="edge17" class="edge">
<title>24&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M743.13,-3390.04C714.99,-3454.91 687.25,-3518.88 668.31,-3562.56"/>
<polygon fill="none" stroke="black" points="658.63,-3558.49 656.33,-3590.2 677.89,-3566.85 658.63,-3558.49"/>
</g>
<!-- 24&#45;&gt;3 -->
<g id="edge15" class="edge">
<title>24&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1014.79,-3390.07C1020.93,-3402.11 1026.73,-3414.19 1032.05,-3426.2 1159.41,-3713.44 1237.86,-4075.1 1274.45,-4273.03"/>
<polygon fill="none" stroke="black" points="1264.16,-4275.14 1279.85,-4302.77 1284.82,-4271.38 1264.16,-4275.14"/>
</g>
<!-- 24&#45;&gt;6 -->
<g id="edge14" class="edge">
<title>24&#45;&gt;6</title>
<path fill="none" stroke="black" d="M877.56,-3390.04C882.82,-3437.22 888.03,-3483.93 892.36,-3522.69"/>
<polygon fill="none" stroke="black" points="881.93,-3523.93 895.69,-3552.58 902.8,-3521.6 881.93,-3523.93"/>
</g>
<!-- 24&#45;&gt;20 -->
<g id="edge16" class="edge">
<title>24&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1080.05,-3376.46C1084.75,-3381.09 1089.42,-3385.67 1094.05,-3390.2 1110.68,-3406.47 1115.96,-3409.4 1132.05,-3426.2 1163.43,-3458.97 1196.48,-3496.35 1224.82,-3529.4"/>
<polygon fill="none" stroke="black" points="1217.09,-3536.52 1244.53,-3552.56 1233.09,-3522.91 1217.09,-3536.52"/>
</g>
<!-- 25 -->
<g id="node26" class="node">
<title>25</title>
<polygon fill="#f2f2f2" stroke="black" points="572.82,-1947.9 572.82,-2526.3 1127.28,-2526.3 1127.28,-1947.9 572.82,-1947.9"/>
<text text-anchor="middle" x="850.05" y="-2509.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="850.05" y="-2492.9" font-family="Times,serif" font-size="14.00">ERC4626</text>
<text text-anchor="middle" x="850.05" y="-2476.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="572.82,-2467.9 1127.28,-2467.9 "/>
<text text-anchor="start" x="580.82" y="-2451.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="580.82" y="-2434.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_asset: IERC20</text>
<text text-anchor="start" x="580.82" y="-2417.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_underlyingDecimals: uint8</text>
<polyline fill="none" stroke="black" points="572.82,-2409.5 1127.28,-2409.5 "/>
<text text-anchor="start" x="580.82" y="-2392.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="580.82" y="-2376.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_tryGetAssetDecimals(asset_: IERC20): (bool, uint8)</text>
<text text-anchor="start" x="580.82" y="-2359.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="580.82" y="-2342.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToShares(assets: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="580.82" y="-2325.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_convertToAssets(shares: uint256, rounding: Math.Rounding): uint256</text>
<text text-anchor="start" x="580.82" y="-2308.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_deposit(caller: address, receiver: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="580.82" y="-2292.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_withdraw(caller: address, receiver: address, owner: address, assets: uint256, shares: uint256)</text>
<text text-anchor="start" x="580.82" y="-2275.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_decimalsOffset(): uint8</text>
<text text-anchor="start" x="580.82" y="-2258.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="580.82" y="-2241.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor(asset_: IERC20)</text>
<text text-anchor="start" x="580.82" y="-2224.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="580.82" y="-2208.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="580.82" y="-2191.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="580.82" y="-2174.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="580.82" y="-2157.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="580.82" y="-2140.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxDeposit(address): uint256</text>
<text text-anchor="start" x="580.82" y="-2124.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxMint(address): uint256</text>
<text text-anchor="start" x="580.82" y="-2107.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="580.82" y="-2090.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxRedeem(owner: address): uint256</text>
<text text-anchor="start" x="580.82" y="-2073.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewDeposit(assets: uint256): uint256</text>
<text text-anchor="start" x="580.82" y="-2056.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewMint(shares: uint256): uint256</text>
<text text-anchor="start" x="580.82" y="-2040.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewWithdraw(assets: uint256): uint256</text>
<text text-anchor="start" x="580.82" y="-2023.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewRedeem(shares: uint256): uint256</text>
<text text-anchor="start" x="580.82" y="-2006.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;deposit(assets: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="580.82" y="-1989.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mint(shares: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="580.82" y="-1972.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdraw(assets: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="580.82" y="-1956.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeem(shares: uint256, receiver: address, owner: address): uint256</text>
</g>
<!-- 25&#45;&gt;3 -->
<g id="edge21" class="edge">
<title>25&#45;&gt;3</title>
<path fill="none" stroke="black" d="M572.4,-2381.18C387.65,-2493.3 159.95,-2668.2 53.05,-2894.8 -40.89,-3093.93 11.42,-3173.99 53.05,-3390.2 91.3,-3588.84 88.78,-3660.62 222.05,-3812.8 454.84,-4078.62 833.79,-4253.34 1074.53,-4344.16"/>
<polygon fill="black" stroke="black" points="1073.51,-4347.52 1084.11,-4347.76 1075.97,-4340.96 1073.51,-4347.52"/>
</g>
<!-- 25&#45;&gt;7 -->
<g id="edge20" class="edge">
<title>25&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M690.79,-2526.43C619.63,-2655.27 537.14,-2804.6 471.27,-2923.86"/>
<polygon fill="black" stroke="black" points="468.12,-2922.31 466.35,-2932.75 474.25,-2925.69 468.12,-2922.31"/>
</g>
<!-- 25&#45;&gt;8 -->
<g id="edge23" class="edge">
<title>25&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M572.46,-2392.7C398.77,-2506.46 189.84,-2678.68 91.05,-2894.8 45.28,-2994.92 45.65,-3289.91 91.05,-3390.2 120.15,-3454.48 177.29,-3507.84 230.61,-3546.77"/>
<polygon fill="black" stroke="black" points="228.62,-3549.65 238.79,-3552.65 232.71,-3543.97 228.62,-3549.65"/>
</g>
<!-- 25&#45;&gt;20 -->
<g id="edge22" class="edge">
<title>25&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M974.27,-2526.46C1017.75,-2639.33 1062.34,-2771.17 1089.05,-2894.8 1135.69,-3110.64 1049.03,-3183.62 1127.05,-3390.2 1148.76,-3447.67 1189.69,-3502.95 1226.18,-3544.71"/>
<polygon fill="black" stroke="black" points="1223.69,-3547.18 1232.94,-3552.35 1228.93,-3542.54 1223.69,-3547.18"/>
</g>
<!-- 25&#45;&gt;22 -->
<g id="edge19" class="edge">
<title>25&#45;&gt;22</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1040.59,-2526.43C1123.89,-2652.48 1220.15,-2798.15 1298.09,-2916.08"/>
<polygon fill="none" stroke="black" points="1289.38,-2921.95 1314.68,-2941.19 1306.9,-2910.37 1289.38,-2921.95"/>
</g>
<!-- 25&#45;&gt;23 -->
<g id="edge24" class="edge">
<title>25&#45;&gt;23</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1097.47,-2526.3C1217.98,-2648.14 1372.52,-2779.86 1538.05,-2858.8 1631.04,-2903.15 1672.83,-2853.13 1767.05,-2894.8 1838.12,-2926.23 1906.6,-2978.38 1960.77,-3026.66"/>
<polygon fill="black" stroke="black" points="1958.54,-3029.36 1968.32,-3033.44 1963.22,-3024.15 1958.54,-3029.36"/>
</g>
<!-- 25&#45;&gt;24 -->
<g id="edge18" class="edge">
<title>25&#45;&gt;24</title>
<path fill="none" stroke="black" d="M850.05,-2526.43C850.05,-2634.75 850.05,-2757.57 850.05,-2864.81"/>
<polygon fill="none" stroke="black" points="839.55,-2864.99 850.05,-2894.99 860.55,-2864.99 839.55,-2864.99"/>
</g>
<!-- 26 -->
<g id="node27" class="node">
<title>26</title>
<polygon fill="#f2f2f2" stroke="black" points="2102.3,-1615.9 2102.3,-2858.3 2929.8,-2858.3 2929.8,-1615.9 2102.3,-1615.9"/>
<text text-anchor="middle" x="2516.05" y="-2841.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="2516.05" y="-2824.9" font-family="Times,serif" font-size="14.00">IPToken</text>
<text text-anchor="middle" x="2516.05" y="-2808.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2102.3,-2799.9 2929.8,-2799.9 "/>
<text text-anchor="start" x="2110.3" y="-2783.3" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2110.3" y="-2766.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mint(tokenAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2749.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;deposit(mintAmount: uint256, receiver: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2732.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeem(redeemTokens: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2716.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;withdraw(redeemAmount: uint256, receiver: address, owner: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2699.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrow(borrowAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2682.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowOnBehalfOf(onBehalfOf: address, borrowAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2665.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrow(repayAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2648.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowOnBehalfOf(onBehalfOf: address, repayAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2632.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrow(borrower: address, repayAmount: uint256, pTokenCollateral: IPToken)</text>
<text text-anchor="start" x="2110.3" y="-2615.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrueInterest()</text>
<text text-anchor="start" x="2110.3" y="-2598.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seize(liquidator: address, borrower: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2581.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;addReserves(addAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2564.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveFactor(newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2548.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowRateMax(newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2531.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setProtocolSeizeShare(newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2514.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesEmergency(reduceAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2497.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesOwner(reduceAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2480.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reduceReservesConfigurator(reduceAmount: uint256)</text>
<text text-anchor="start" x="2110.3" y="-2464.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;sweepToken(token: IERC20)</text>
<text text-anchor="start" x="2110.3" y="-2447.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRateMaxMantissa(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2430.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accrualBlockTimestamp(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2413.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateCurrent(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2396.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOfUnderlying(owner: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2380.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountSnapshot(account: address): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="2110.3" y="-2363.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrowsCurrent(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2346.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReservesCurrent(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2329.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReservesCurrent(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2312.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReservesCurrent(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2296.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceCurrent(account: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2279.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowBalanceStored(account: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-2262.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exchangeRateStored(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2245.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getCash(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2228.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowRatePerSecond(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2212.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyRatePerSecond(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2195.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalBorrows(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2178.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalReserves(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2161.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;ownerReserves(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2144.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configuratorReserves(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2128.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowIndex(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2111.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;reserveFactorMantissa(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2094.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;riskEngine(): IRiskEngine</text>
<text text-anchor="start" x="2110.3" y="-2077.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="2110.3" y="-2060.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="2110.3" y="-2044.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="2110.3" y="-2027.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="2110.3" y="-2010.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="2110.3" y="-1993.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;protocolSeizeShareMantissa(): uint256</text>
<text text-anchor="start" x="2110.3" y="-1976.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="2110.3" y="-1960.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="2110.3" y="-1943.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxMint(receiver: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-1926.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxDeposit(account: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-1909.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1892.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="2110.3" y="-1876.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1859.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1842.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1825.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1808.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="2110.3" y="-1792.1" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2110.3" y="-1775.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewRiskEngine(oldRiskEngine: IRiskEngine, newRiskEngine: IRiskEngine)</text>
<text text-anchor="start" x="2110.3" y="-1758.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Borrow(borrower: address, onBehalfOf: address, borrowAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1741.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; RepayBorrow(payer: address, onBehalfOf: address, repayAmount: uint256, accountBorrows: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1724.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveFactor(oldReserveFactorMantissa: uint256, newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1708.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowRateMax(oldBorrowRateMaxMantissa: uint256, newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1691.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewProtocolSeizeShare(oldProtocolSeizeShareMantissa: uint256, newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1674.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; AccrueInterest(cashPrior: uint256, totalReserves: uint256, borrowIndex: uint256, totalBorrows: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1657.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; LiquidateBorrow(liquidator: address, borrower: address, repayAmount: uint256, pTokenCollateral: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1640.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesAdded(benefactor: address, addAmount: uint256, newTotalReserves: uint256)</text>
<text text-anchor="start" x="2110.3" y="-1624.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ReservesReduced(admin: address, reduceAmount: uint256, newTotalReserves: uint256)</text>
</g>
<!-- 26&#45;&gt;3 -->
<g id="edge28" class="edge">
<title>26&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2582.03,-2858.71C2584.75,-3167.97 2539.64,-3534.31 2359.05,-3812.8 2166.16,-4110.26 1772.97,-4277.81 1523.46,-4358.25"/>
<polygon fill="black" stroke="black" points="1522.37,-4354.92 1513.91,-4361.31 1524.5,-4361.59 1522.37,-4354.92"/>
</g>
<!-- 26&#45;&gt;22 -->
<g id="edge25" class="edge">
<title>26&#45;&gt;22</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2101.9,-2827.94C2086.67,-2838.94 2071.05,-2849.26 2055.05,-2858.8 1958.62,-2916.27 1909.58,-2853.87 1805.05,-2894.8 1781.87,-2903.87 1758.73,-2914.7 1735.99,-2926.68"/>
<polygon fill="none" stroke="black" points="1730.74,-2917.59 1709.51,-2941.24 1740.86,-2935.99 1730.74,-2917.59"/>
</g>
<!-- 26&#45;&gt;26 -->
<g id="edge27" class="edge">
<title>26&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2929.96,-2150.93C2941.37,-2174.61 2947.8,-2203.33 2947.8,-2237.1 2947.8,-2266.51 2942.92,-2292.1 2934.12,-2313.86"/>
<polygon fill="black" stroke="black" points="2930.8,-2312.71 2929.96,-2323.27 2937.21,-2315.54 2930.8,-2312.71"/>
</g>
<!-- 27 -->
<g id="node28" class="node">
<title>27</title>
<polygon fill="#f2f2f2" stroke="black" points="3161.51,-3849.3 3161.51,-4990.9 4154.59,-4990.9 4154.59,-3849.3 3161.51,-3849.3"/>
<text text-anchor="middle" x="3658.05" y="-4974.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="3658.05" y="-4957.5" font-family="Times,serif" font-size="14.00">IRiskEngine</text>
<text text-anchor="middle" x="3658.05" y="-4940.7" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3161.51,-4932.5 4154.59,-4932.5 "/>
<text text-anchor="start" x="3169.51" y="-4915.9" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="3169.51" y="-4899.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3169.51" y="-4882.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;switchEMode(newCategoryId: uint8)</text>
<text text-anchor="start" x="3169.51" y="-4865.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;enterMarkets(pTokens: address[]): uint256[]</text>
<text text-anchor="start" x="3169.51" y="-4848.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;exitMarket(pTokenAddress: address)</text>
<text text-anchor="start" x="3169.51" y="-4831.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;updateDelegate(delegate: address, approved: bool)</text>
<text text-anchor="start" x="3169.51" y="-4815.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintVerify(account: address)</text>
<text text-anchor="start" x="3169.51" y="-4798.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowVerify(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3169.51" y="-4781.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowAllowed(pToken: address, borrower: address, borrowAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4764.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setOracle(newOracle: address)</text>
<text text-anchor="start" x="3169.51" y="-4747.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureEMode(categoryId: uint8, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3169.51" y="-4731.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setCloseFactor(pTokenAddress: address, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="3169.51" y="-4714.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;configureMarket(pToken: IPToken, baseConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3169.51" y="-4697.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportMarket(pToken: IPToken)</text>
<text text-anchor="start" x="3169.51" y="-4680.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supportEMode(categoryId: uint8, isAllowed: bool, pTokens: address[], collateralPermissions: bool[], borrowPermissions: bool[])</text>
<text text-anchor="start" x="3169.51" y="-4663.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketBorrowCaps(pTokens: IPToken[], newBorrowCaps: uint256[])</text>
<text text-anchor="start" x="3169.51" y="-4647.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMarketSupplyCaps(pTokens: IPToken[], newSupplyCaps: uint256[])</text>
<text text-anchor="start" x="3169.51" y="-4630.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setMintPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="3169.51" y="-4613.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setBorrowPaused(pToken: IPToken, state: bool): bool</text>
<text text-anchor="start" x="3169.51" y="-4596.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setTransferPaused(state: bool): bool</text>
<text text-anchor="start" x="3169.51" y="-4579.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;setSeizePaused(state: bool): bool</text>
<text text-anchor="start" x="3169.51" y="-4563.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAssetsIn(account: address): IPToken[]</text>
<text text-anchor="start" x="3169.51" y="-4546.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getReserveShares(): (ownerShareMantissa: uint256, configuratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3169.51" y="-4529.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkCollateralMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="3169.51" y="-4512.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;checkBorrowMembership(account: address, pToken: IPToken): bool</text>
<text text-anchor="start" x="3169.51" y="-4495.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;accountCategory(account: address): uint8</text>
<text text-anchor="start" x="3169.51" y="-4479.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3169.51" y="-4462.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAccountBorrowLiquidity(account: address): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3169.51" y="-4445.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getHypotheticalAccountLiquidity(account: address, pTokenModify: address, redeemTokens: uint256, borrowAmount: uint256): (RiskEngineError.Error, uint256, uint256)</text>
<text text-anchor="start" x="3169.51" y="-4428.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateCalculateSeizeTokens(borrower: address, pTokenBorrowed: address, pTokenCollateral: address, actualRepayAmount: uint256): (RiskEngineError.Error, uint256)</text>
<text text-anchor="start" x="3169.51" y="-4411.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;delegateAllowed(user: address, delegate: address): bool</text>
<text text-anchor="start" x="3169.51" y="-4395.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;getAllMarkets(): IPToken[]</text>
<text text-anchor="start" x="3169.51" y="-4378.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;isDeprecated(pToken: IPToken): bool</text>
<text text-anchor="start" x="3169.51" y="-4361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;maxWithdraw(pToken: address, account: address): uint256</text>
<text text-anchor="start" x="3169.51" y="-4344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;mintAllowed(account: address, pToken: address, mintAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;redeemAllowed(pToken: address, redeemer: address, redeemTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4311.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;repayBorrowAllowed(pToken: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4294.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidateBorrowAllowed(pTokenBorrowed: address, pTokenCollateral: address, borrower: address, repayAmount: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4277.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;seizeAllowed(pTokenCollateral: address, pTokenBorrowed: address): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4260.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferAllowed(pToken: address, src: address, transferTokens: uint256): RiskEngineError.Error</text>
<text text-anchor="start" x="3169.51" y="-4243.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;oracle(): address</text>
<text text-anchor="start" x="3169.51" y="-4227.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;collateralFactor(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="3169.51" y="-4210.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationThreshold(categoryId: uint8, pToken: IPToken): uint256</text>
<text text-anchor="start" x="3169.51" y="-4193.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;liquidationIncentive(categoryId: uint8, pToken: address): uint256</text>
<text text-anchor="start" x="3169.51" y="-4176.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;closeFactor(pToken: address): uint256</text>
<text text-anchor="start" x="3169.51" y="-4159.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;supplyCap(pToken: address): uint256</text>
<text text-anchor="start" x="3169.51" y="-4143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;borrowCap(pToken: address): uint256</text>
<text text-anchor="start" x="3169.51" y="-4126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;emodeMarkets(categoryId: uint8): (collateralTokens: address[], borrowTokens: address[])</text>
<text text-anchor="start" x="3169.51" y="-4109.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="3169.51" y="-4092.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewEModeConfiguration(categoryId: uint8, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3169.51" y="-4075.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewMarketConfiguration(pToken: IPToken, oldConfig: BaseConfiguration, newConfig: BaseConfiguration)</text>
<text text-anchor="start" x="3169.51" y="-4059.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewOracleEngine(oldOracleEngine: address, newOracleEngine: address)</text>
<text text-anchor="start" x="3169.51" y="-4042.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewReserveShares(newOwnerShareMantissa: uint256, newConfiguratorShareMantissa: uint256)</text>
<text text-anchor="start" x="3169.51" y="-4025.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketListed(pToken: IPToken)</text>
<text text-anchor="start" x="3169.51" y="-4008.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeSwitched(account: address, oldCategory: uint8, newCategory: uint8)</text>
<text text-anchor="start" x="3169.51" y="-3991.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EModeUpdated(categoryId: uint8, pToken: address, allowed: bool, collateralStatus: bool, borrowStatus: bool)</text>
<text text-anchor="start" x="3169.51" y="-3975.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketEntered(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3169.51" y="-3958.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; MarketExited(pToken: IPToken, account: address)</text>
<text text-anchor="start" x="3169.51" y="-3941.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewCloseFactor(pToken: address, oldCloseFactorMantissa: uint256, newCloseFactorMantissa: uint256)</text>
<text text-anchor="start" x="3169.51" y="-3924.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(action: string, pauseState: bool)</text>
<text text-anchor="start" x="3169.51" y="-3907.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; ActionPaused(pToken: IPToken, action: string, pauseState: bool)</text>
<text text-anchor="start" x="3169.51" y="-3891.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewBorrowCap(pToken: IPToken, newBorrowCap: uint256)</text>
<text text-anchor="start" x="3169.51" y="-3874.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; NewSupplyCap(pToken: IPToken, newSupplyCap: uint256)</text>
<text text-anchor="start" x="3169.51" y="-3857.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; DelegateUpdated(approver: address, delegate: address, approved: bool)</text>
</g>
<!-- 26&#45;&gt;27 -->
<g id="edge26" class="edge">
<title>26&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2626.9,-2858.66C2710.75,-3171.82 2851.55,-3541.69 3080.05,-3812.8 3100.83,-3837.46 3118.02,-3828.47 3143.05,-3848.8 3146.61,-3851.69 3150.17,-3854.62 3153.73,-3857.56"/>
<polygon fill="black" stroke="black" points="3151.59,-3860.33 3161.51,-3864.05 3156.07,-3854.96 3151.59,-3860.33"/>
</g>
<!-- 27&#45;&gt;11 -->
<g id="edge32" class="edge">
<title>27&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3800.18,-4991.16C3842.04,-5125.01 3888.99,-5243.97 3935.05,-5296.4 3946.28,-5309.19 3960.58,-5319.62 3975.71,-5328.09"/>
<polygon fill="black" stroke="black" points="3974.22,-5331.26 3984.69,-5332.84 3977.49,-5325.07 3974.22,-5331.26"/>
</g>
<!-- 27&#45;&gt;12 -->
<g id="edge33" class="edge">
<title>27&#45;&gt;12</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3978.61,-4991.17C3983.96,-5000.69 3989.17,-5009.94 3994.21,-5018.91"/>
<polygon fill="black" stroke="black" points="3991.24,-5020.78 3999.19,-5027.78 3997.34,-5017.35 3991.24,-5020.78"/>
</g>
<!-- 27&#45;&gt;26 -->
<g id="edge31" class="edge">
<title>27&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3161.44,-3849.11C3161.31,-3849.01 3161.18,-3848.9 3161.05,-3848.8 3136.02,-3828.47 3118.83,-3837.46 3098.05,-3812.8 2871.89,-3544.47 2731.64,-3179.39 2645.61,-2868.31"/>
<polygon fill="black" stroke="black" points="2648.99,-2867.37 2642.96,-2858.66 2642.24,-2869.23 2648.99,-2867.37"/>
</g>
<!-- 28 -->
<g id="node29" class="node">
<title>28</title>
<polygon fill="#f2f2f2" stroke="black" points="3537.68,-5103.5 3537.68,-5220.3 3778.42,-5220.3 3778.42,-5103.5 3537.68,-5103.5"/>
<text text-anchor="middle" x="3658.05" y="-5203.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3658.05" y="-5186.9" font-family="Times,serif" font-size="14.00">BaseConfiguration</text>
<text text-anchor="middle" x="3658.05" y="-5170.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3537.68,-5161.9 3778.42,-5161.9 "/>
<text text-anchor="start" x="3545.68" y="-5145.3" font-family="Times,serif" font-size="14.00">collateralFactorMantissa: uint256</text>
<text text-anchor="start" x="3545.68" y="-5128.5" font-family="Times,serif" font-size="14.00">liquidationThresholdMantissa: uint256</text>
<text text-anchor="start" x="3545.68" y="-5111.7" font-family="Times,serif" font-size="14.00">liquidationIncentiveMantissa: uint256</text>
</g>
<!-- 27&#45;&gt;28 -->
<g id="edge30" class="edge">
<title>27&#45;&gt;28</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3652.27,-4991.17C3652.69,-5030.36 3653.25,-5065.26 3653.95,-5093.07"/>
<polygon fill="black" stroke="black" points="3650.46,-5093.47 3654.22,-5103.37 3657.45,-5093.28 3650.46,-5093.47"/>
</g>
<!-- 28&#45;&gt;27 -->
<g id="edge29" class="edge">
<title>28&#45;&gt;27</title>
<path fill="none" stroke="black" d="M3661.88,-5103.37C3662.63,-5076.79 3663.23,-5042.4 3663.7,-5003.2"/>
<polygon fill="black" stroke="black" points="3663.7,-5003.17 3659.76,-4997.13 3663.83,-4991.17 3667.76,-4997.22 3663.7,-5003.17"/>
</g>
<!-- 29 -->
<g id="node30" class="node">
<title>29</title>
<polygon fill="#f2f2f2" stroke="black" points="3060.05,-3046.5 3060.05,-3238.5 3398.05,-3238.5 3398.05,-3046.5 3060.05,-3046.5"/>
<text text-anchor="middle" x="3229.05" y="-3221.9" font-family="Times,serif" font-size="14.00">PTokenStorage</text>
<text text-anchor="middle" x="3229.05" y="-3205.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3060.05,-3196.9 3398.05,-3196.9 "/>
<text text-anchor="start" x="3068.05" y="-3180.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3068.05" y="-3163.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_PTOKEN_STORAGE: bytes32</text>
<text text-anchor="start" x="3068.05" y="-3146.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_SLOT_PTOKEN_TRANSIENT_STORAGE: bytes32</text>
<text text-anchor="start" x="3068.05" y="-3129.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;_MANTISSA_ONE: uint256</text>
<text text-anchor="start" x="3068.05" y="-3113.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_RESERVE_FACTOR_MAX_MANTISSA: uint256</text>
<text text-anchor="start" x="3068.05" y="-3096.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;MINIMUM_DEAD_SHARES: uint256</text>
<polyline fill="none" stroke="black" points="3060.05,-3088.1 3398.05,-3088.1 "/>
<text text-anchor="start" x="3068.05" y="-3071.5" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="3068.05" y="-3054.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getPTokenStorage(): (data: PTokenData)</text>
</g>
<!-- 30 -->
<g id="node31" class="node">
<title>30</title>
<polygon fill="#f2f2f2" stroke="black" points="3098.45,-3426.7 3098.45,-3812.3 3505.65,-3812.3 3505.65,-3426.7 3098.45,-3426.7"/>
<text text-anchor="middle" x="3302.05" y="-3795.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3302.05" y="-3778.9" font-family="Times,serif" font-size="14.00">PTokenData</text>
<text text-anchor="middle" x="3302.05" y="-3762.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3098.45,-3753.9 3505.65,-3753.9 "/>
<text text-anchor="start" x="3106.45" y="-3737.3" font-family="Times,serif" font-size="14.00">underlying: address</text>
<text text-anchor="start" x="3106.45" y="-3720.5" font-family="Times,serif" font-size="14.00">name: string</text>
<text text-anchor="start" x="3106.45" y="-3703.7" font-family="Times,serif" font-size="14.00">symbol: string</text>
<text text-anchor="start" x="3106.45" y="-3686.9" font-family="Times,serif" font-size="14.00">decimals: uint8</text>
<text text-anchor="start" x="3106.45" y="-3670.1" font-family="Times,serif" font-size="14.00">riskEngine: IRiskEngine</text>
<text text-anchor="start" x="3106.45" y="-3653.3" font-family="Times,serif" font-size="14.00">initialExchangeRateMantissa: uint256</text>
<text text-anchor="start" x="3106.45" y="-3636.5" font-family="Times,serif" font-size="14.00">protocolSeizeShareMantissa: uint256</text>
<text text-anchor="start" x="3106.45" y="-3619.7" font-family="Times,serif" font-size="14.00">reserveFactorMantissa: uint256</text>
<text text-anchor="start" x="3106.45" y="-3602.9" font-family="Times,serif" font-size="14.00">borrowRateMaxMantissa: uint256</text>
<text text-anchor="start" x="3106.45" y="-3586.1" font-family="Times,serif" font-size="14.00">accrualBlockTimestamp: uint256</text>
<text text-anchor="start" x="3106.45" y="-3569.3" font-family="Times,serif" font-size="14.00">borrowIndex: uint256</text>
<text text-anchor="start" x="3106.45" y="-3552.5" font-family="Times,serif" font-size="14.00">totalBorrows: uint256</text>
<text text-anchor="start" x="3106.45" y="-3535.7" font-family="Times,serif" font-size="14.00">totalReserves: uint256</text>
<text text-anchor="start" x="3106.45" y="-3518.9" font-family="Times,serif" font-size="14.00">totalSupply: uint256</text>
<text text-anchor="start" x="3106.45" y="-3502.1" font-family="Times,serif" font-size="14.00">ownerReserves: uint256</text>
<text text-anchor="start" x="3106.45" y="-3485.3" font-family="Times,serif" font-size="14.00">configuratorReserves: uint256</text>
<text text-anchor="start" x="3106.45" y="-3468.5" font-family="Times,serif" font-size="14.00">accountTokens: mapping(address=&gt;uint256)</text>
<text text-anchor="start" x="3106.45" y="-3451.7" font-family="Times,serif" font-size="14.00">transferAllowances: mapping(address=&gt;mapping(address=&gt;uint256))</text>
<text text-anchor="start" x="3106.45" y="-3434.9" font-family="Times,serif" font-size="14.00">accountBorrows: mapping(address=&gt;BorrowSnapshot)</text>
</g>
<!-- 29&#45;&gt;30 -->
<g id="edge38" class="edge">
<title>29&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3238.16,-3238.77C3245.02,-3289.92 3254.64,-3355.02 3264.37,-3416.81"/>
<polygon fill="black" stroke="black" points="3260.92,-3417.36 3265.93,-3426.69 3267.83,-3416.27 3260.92,-3417.36"/>
</g>
<!-- 30&#45;&gt;27 -->
<g id="edge39" class="edge">
<title>30&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3387.66,-3812.55C3391.63,-3821.46 3395.69,-3830.56 3399.82,-3839.83"/>
<polygon fill="black" stroke="black" points="3396.71,-3841.44 3403.98,-3849.15 3403.1,-3838.59 3396.71,-3841.44"/>
</g>
<!-- 30&#45;&gt;29 -->
<g id="edge34" class="edge">
<title>30&#45;&gt;29</title>
<path fill="none" stroke="black" d="M3279.21,-3426.69C3270.29,-3366.7 3260.15,-3302.66 3251.27,-3250.69"/>
<polygon fill="black" stroke="black" points="3251.26,-3250.59 3246.3,-3245.36 3249.22,-3238.77 3254.18,-3244 3251.26,-3250.59"/>
</g>
<!-- 32 -->
<g id="node33" class="node">
<title>32</title>
<polygon fill="#f2f2f2" stroke="black" points="2902.68,-4370.1 2902.68,-4470.1 3143.42,-4470.1 3143.42,-4370.1 2902.68,-4370.1"/>
<text text-anchor="middle" x="3023.05" y="-4453.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3023.05" y="-4436.7" font-family="Times,serif" font-size="14.00">BorrowSnapshot</text>
<text text-anchor="middle" x="3023.05" y="-4419.9" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2902.68,-4411.7 3143.42,-4411.7 "/>
<text text-anchor="start" x="2910.68" y="-4395.1" font-family="Times,serif" font-size="14.00">principal: uint256</text>
<text text-anchor="start" x="2910.68" y="-4378.3" font-family="Times,serif" font-size="14.00">interestIndex: uint256</text>
</g>
<!-- 30&#45;&gt;32 -->
<g id="edge40" class="edge">
<title>30&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3169.23,-3812.45C3162.99,-3824.52 3157.2,-3836.69 3152.05,-3848.8 3076.73,-4026.01 3042.21,-4253.71 3029.37,-4359.79"/>
<polygon fill="black" stroke="black" points="3025.87,-4359.63 3028.16,-4369.98 3032.82,-4360.46 3025.87,-4359.63"/>
</g>
<!-- 31 -->
<g id="node32" class="node">
<title>31</title>
<polygon fill="#f2f2f2" stroke="black" points="3041.68,-2195.5 3041.68,-2278.7 3282.42,-2278.7 3282.42,-2195.5 3041.68,-2195.5"/>
<text text-anchor="middle" x="3162.05" y="-2262.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3162.05" y="-2245.3" font-family="Times,serif" font-size="14.00">PTokenTransientData</text>
<text text-anchor="middle" x="3162.05" y="-2228.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3041.68,-2220.3 3282.42,-2220.3 "/>
<text text-anchor="start" x="3049.68" y="-2203.7" font-family="Times,serif" font-size="14.00">_entered: bytes32</text>
</g>
<!-- 31&#45;&gt;29 -->
<g id="edge35" class="edge">
<title>31&#45;&gt;29</title>
<path fill="none" stroke="black" d="M3165.08,-2278.97C3174.93,-2411.81 3206.27,-2834.32 3221.11,-3034.44"/>
<polygon fill="black" stroke="black" points="3221.11,-3034.45 3225.54,-3040.14 3222,-3046.42 3217.57,-3040.73 3221.11,-3034.45"/>
</g>
<!-- 32&#45;&gt;29 -->
<g id="edge36" class="edge">
<title>32&#45;&gt;29</title>
<path fill="none" stroke="black" d="M3019.63,-4369.95C3010.85,-4223.4 2994.36,-3777.76 3089.05,-3426.2 3105.61,-3364.73 3135.99,-3300.8 3164.23,-3249.32"/>
<polygon fill="black" stroke="black" points="3164.39,-3249.02 3163.81,-3241.83 3170.22,-3238.53 3170.8,-3245.72 3164.39,-3249.02"/>
</g>
<!-- 33 -->
<g id="node34" class="node">
<title>33</title>
<polygon fill="#f2f2f2" stroke="black" points="3300.68,-2161.9 3300.68,-2312.3 3541.42,-2312.3 3541.42,-2161.9 3300.68,-2161.9"/>
<text text-anchor="middle" x="3421.05" y="-2295.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="3421.05" y="-2278.9" font-family="Times,serif" font-size="14.00">PendingSnapshot</text>
<text text-anchor="middle" x="3421.05" y="-2262.1" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="3300.68,-2253.9 3541.42,-2253.9 "/>
<text text-anchor="start" x="3308.68" y="-2237.3" font-family="Times,serif" font-size="14.00">totalBorrow: uint256</text>
<text text-anchor="start" x="3308.68" y="-2220.5" font-family="Times,serif" font-size="14.00">totalReserve: uint256</text>
<text text-anchor="start" x="3308.68" y="-2203.7" font-family="Times,serif" font-size="14.00">accBorrowIndex: uint256</text>
<text text-anchor="start" x="3308.68" y="-2186.9" font-family="Times,serif" font-size="14.00">ownerReserve: uint256</text>
<text text-anchor="start" x="3308.68" y="-2170.1" font-family="Times,serif" font-size="14.00">configuratorReserve: uint256</text>
</g>
<!-- 33&#45;&gt;29 -->
<g id="edge37" class="edge">
<title>33&#45;&gt;29</title>
<path fill="none" stroke="black" d="M3405.67,-2312.67C3381.57,-2429.27 3333.28,-2661.8 3291.05,-2858.8 3278.59,-2916.92 3264.41,-2981.7 3252.75,-3034.56"/>
<polygon fill="black" stroke="black" points="3252.74,-3034.63 3255.35,-3041.35 3250.15,-3046.35 3247.54,-3039.63 3252.74,-3034.63"/>
</g>
<!-- 34 -->
<g id="node35" class="node">
<title>34</title>
<polygon fill="#f2f2f2" stroke="black" points="2777.73,-0.5 2777.73,-1578.9 4360.37,-1578.9 4360.37,-0.5 2777.73,-0.5"/>
<text text-anchor="middle" x="3569.05" y="-1562.3" font-family="Times,serif" font-size="14.00">PTokenModule</text>
<text text-anchor="middle" x="3569.05" y="-1545.5" font-family="Times,serif" font-size="14.00">public/flatten/PTokenModule.flatten.sol</text>
<polyline fill="none" stroke="black" points="2777.73,-1537.3 4360.37,-1537.3 "/>
<text text-anchor="start" x="2785.73" y="-1520.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="2785.73" y="-1503.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mintFresh(minter: address, onBehalfOf: address, mintTokensIn: uint256, mintAmountIn: uint256): (uint256, uint256)</text>
<text text-anchor="start" x="2785.73" y="-1487.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeemFresh(receiver: address, onBehalfOf: address, redeemTokensIn: uint256, redeemAmountIn: uint256): (uint256, uint256)</text>
<text text-anchor="start" x="2785.73" y="-1470.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowFresh(borrower: address, onBehalfOf: address, borrowAmount: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1453.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;repayBorrowFresh(payer: address, onBehalfOf: address, repayAmount: uint256): uint256</text>
<text text-anchor="start" x="2785.73" y="-1436.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;liquidateBorrowFresh(liquidator: address, borrower: address, repayAmount: uint256, pTokenCollateral: IPToken)</text>
<text text-anchor="start" x="2785.73" y="-1419.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;seizeInternal(seizerToken: address, liquidator: address, borrower: address, seizeTokens: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1403.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;doTransferIn(from: address, amount: uint256): uint256</text>
<text text-anchor="start" x="2785.73" y="-1386.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;doTransferOut(to: address, amount: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1369.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transferTokens(spender: address, src: address, dst: address, tokens: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1352.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_spendAllowance(owner: address, spender: address, value: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1335.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setProtocolSeizeShareMantissa(newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1319.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setReserveFactorFresh(newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1302.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_addReservesFresh(addAmount: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1285.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_reduceReserves(reduceAmount: uint256, totalReserve: uint256): (newReserve: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1268.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_setRiskEngine(newRiskEngine: IRiskEngine)</text>
<text text-anchor="start" x="2785.73" y="-1251.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_pendingAccruedSnapshot(): (snapshot: PendingSnapshot)</text>
<text text-anchor="start" x="2785.73" y="-1235.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowBalanceStoredInternal(account: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-1218.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;exchangeRateStoredInternal(): uint256</text>
<text text-anchor="start" x="2785.73" y="-1201.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_isDelegateeOf(onBehalfOf: address)</text>
<text text-anchor="start" x="2785.73" y="-1184.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getReserveShares(accumulatedReserve: uint256): (ownerShare: uint256, configuratorShare: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1167.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_getBlockTimestamp(): uint256</text>
<text text-anchor="start" x="2785.73" y="-1151.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_reentrancyGuardEntered(): (result: bool)</text>
<text text-anchor="start" x="2785.73" y="-1134.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkPermission(permission: bytes32, target: address)</text>
<text text-anchor="start" x="2785.73" y="-1117.5" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="2785.73" y="-1100.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;initialize(underlying_: address, riskEngine_: IRiskEngine, initialExchangeRateMantissa_: uint256, reserveFactorMantissa_: uint256, protocolSeizeShareMantissa_: uint256, borrowRateMaxMantissa_: uint256, name_: string, symbol_: string, decimals_: uint8) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-1083.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setBorrowRateMax(newBorrowRateMaxMantissa: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1067.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setReserveFactor(newReserveFactorMantissa: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1050.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setProtocolSeizeShare(newProtocolSeizeShareMantissa: uint256)</text>
<text text-anchor="start" x="2785.73" y="-1033.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;addReserves(addAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-1016.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;reduceReservesEmergency(reduceAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-999.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;reduceReservesOwner(reduceAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-983.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;reduceReservesConfigurator(reduceAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-966.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sweepToken(token: IERC20)</text>
<text text-anchor="start" x="2785.73" y="-949.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transfer(dst: address, amount: uint256): bool &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-932.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferFrom(src: address, dst: address, amount: uint256): bool &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-915.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;mint(tokenAmount: uint256, receiver: address): uint256 &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-899.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;deposit(mintAmount: uint256, receiver: address): uint256 &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-882.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;redeem(redeemTokens: uint256, receiver: address, owner: address): uint256 &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-865.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdraw(redeemAmount: uint256, receiver: address, owner: address): uint256 &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-848.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrow(borrowAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-831.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowOnBehalfOf(onBehalfOf: address, borrowAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-815.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;repayBorrow(repayAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-798.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;repayBorrowOnBehalfOf(onBehalfOf: address, repayAmount: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-781.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;liquidateBorrow(borrower: address, repayAmount: uint256, pTokenCollateral: IPToken) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-764.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;seize(liquidator: address, borrower: address, seizeTokens: uint256) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="2785.73" y="-747.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;approve(spender: address, amount: uint256): bool</text>
<text text-anchor="start" x="2785.73" y="-731.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-714.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToShares(assets: uint256): uint256</text>
<text text-anchor="start" x="2785.73" y="-697.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;convertToAssets(shares: uint256): uint256</text>
<text text-anchor="start" x="2785.73" y="-680.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxMint(receiver: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-663.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxWithdraw(owner: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-647.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxRedeem(owner: address): (maxShares: uint256)</text>
<text text-anchor="start" x="2785.73" y="-630.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewDeposit(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2785.73" y="-613.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewMint(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2785.73" y="-596.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewRedeem(shares: uint256): (assets: uint256)</text>
<text text-anchor="start" x="2785.73" y="-579.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;previewWithdraw(assets: uint256): (shares: uint256)</text>
<text text-anchor="start" x="2785.73" y="-563.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;balanceOf(owner: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-546.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;accrualBlockTimestamp(): uint256</text>
<text text-anchor="start" x="2785.73" y="-529.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;riskEngine(): IRiskEngine</text>
<text text-anchor="start" x="2785.73" y="-512.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;reserveFactorMantissa(): uint256</text>
<text text-anchor="start" x="2785.73" y="-495.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowRateMaxMantissa(): uint256</text>
<text text-anchor="start" x="2785.73" y="-479.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowIndex(): uint256</text>
<text text-anchor="start" x="2785.73" y="-462.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalBorrows(): uint256</text>
<text text-anchor="start" x="2785.73" y="-445.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;ownerReserves(): uint256</text>
<text text-anchor="start" x="2785.73" y="-428.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;configuratorReserves(): uint256</text>
<text text-anchor="start" x="2785.73" y="-411.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalReserves(): uint256</text>
<text text-anchor="start" x="2785.73" y="-395.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="2785.73" y="-378.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;name(): string</text>
<text text-anchor="start" x="2785.73" y="-361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;symbol(): string</text>
<text text-anchor="start" x="2785.73" y="-344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="2785.73" y="-327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;balanceOfUnderlying(owner: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-311.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getAccountSnapshot(account: address): (uint256, uint256, uint256)</text>
<text text-anchor="start" x="2785.73" y="-294.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;exchangeRateStored(): uint256</text>
<text text-anchor="start" x="2785.73" y="-277.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowBalanceStored(account: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-260.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowRatePerSecond(): uint256</text>
<text text-anchor="start" x="2785.73" y="-243.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;supplyRatePerSecond(): uint256</text>
<text text-anchor="start" x="2785.73" y="-227.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalBorrowsCurrent(): uint256</text>
<text text-anchor="start" x="2785.73" y="-210.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalReservesCurrent(): uint256</text>
<text text-anchor="start" x="2785.73" y="-193.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;ownerReservesCurrent(): uint256</text>
<text text-anchor="start" x="2785.73" y="-176.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;configuratorReservesCurrent(): uint256</text>
<text text-anchor="start" x="2785.73" y="-159.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;borrowBalanceCurrent(account: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;asset(): address</text>
<text text-anchor="start" x="2785.73" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;protocolSeizeShareMantissa(): uint256</text>
<text text-anchor="start" x="2785.73" y="-109.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="2785.73" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; nonReentrant()</text>
<text text-anchor="start" x="2785.73" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;accrueInterest()</text>
<text text-anchor="start" x="2785.73" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;totalAssets(): uint256</text>
<text text-anchor="start" x="2785.73" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;maxDeposit(account: address): uint256</text>
<text text-anchor="start" x="2785.73" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;exchangeRateCurrent(): uint256</text>
<text text-anchor="start" x="2785.73" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getCash(): uint256</text>
</g>
<!-- 34&#45;&gt;3 -->
<g id="edge50" class="edge">
<title>34&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2777.5,-895.28C2333.27,-998.95 1814.21,-1205.2 1538.05,-1615.4 1229.43,-2073.81 1178.91,-2438.79 1538.05,-2858.8 1605.01,-2937.1 1700.09,-2816.5 1767.05,-2894.8 1838.59,-2978.47 1790.37,-3282.61 1767.05,-3390.2 1692.01,-3736.39 1490.11,-4105.93 1377.56,-4293.77"/>
<polygon fill="black" stroke="black" points="1374.42,-4292.2 1372.27,-4302.58 1380.42,-4295.81 1374.42,-4292.2"/>
</g>
<!-- 34&#45;&gt;9 -->
<g id="edge47" class="edge">
<title>34&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2777.51,-984.54C2427.88,-1109.16 2041.54,-1307.33 1797.05,-1615.4 1654.79,-1794.66 1657.84,-2092.03 1664.02,-2197.53"/>
<polygon fill="black" stroke="black" points="1660.54,-2197.94 1664.66,-2207.7 1667.53,-2197.5 1660.54,-2197.94"/>
</g>
<!-- 34&#45;&gt;10 -->
<g id="edge51" class="edge">
<title>34&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2777.35,-1066.15C2513.13,-1194.73 2240.41,-1373.98 2060.05,-1615.4 1923.06,-1798.77 1920.13,-2092.97 1923.92,-2197.62"/>
<polygon fill="black" stroke="black" points="1920.43,-2197.87 1924.33,-2207.72 1927.42,-2197.59 1920.43,-2197.87"/>
</g>
<!-- 34&#45;&gt;11 -->
<g id="edge55" class="edge">
<title>34&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4360.62,-1575.37C4363.76,-1576.73 4366.9,-1578.08 4370.05,-1579.4 4466.23,-1619.88 6173.62,-1542.26 6248.05,-1615.4 6732.16,-2091.11 6304.05,-2462.78 6304.05,-3141.5 6304.05,-3141.5 6304.05,-3141.5 6304.05,-4421.1 6304.05,-4879.72 4682.22,-5239.75 4204.77,-5335.81"/>
<polygon fill="black" stroke="black" points="4203.85,-5332.42 4194.73,-5337.82 4205.23,-5339.29 4203.85,-5332.42"/>
</g>
<!-- 34&#45;&gt;12 -->
<g id="edge56" class="edge">
<title>34&#45;&gt;12</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3582.27,-1579.11C3593,-2136.56 3609.09,-2795.73 3626.05,-2858.8 3756.08,-3342.4 4040.69,-3363.46 4164.05,-3848.8 4289.14,-4340.97 4286.76,-4498.63 4164.05,-4991.4 4161.83,-5000.3 4159.05,-5009.25 4155.85,-5018.14"/>
<polygon fill="black" stroke="black" points="4152.48,-5017.15 4152.22,-5027.74 4159.03,-5019.62 4152.48,-5017.15"/>
</g>
<!-- 34&#45;&gt;13 -->
<g id="edge53" class="edge">
<title>34&#45;&gt;13</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3779.56,-1578.96C3843.24,-1817.42 3904.36,-2046.28 3934.87,-2160.55"/>
<polygon fill="black" stroke="black" points="3931.5,-2161.49 3937.46,-2170.24 3938.26,-2159.68 3931.5,-2161.49"/>
</g>
<!-- 34&#45;&gt;14 -->
<g id="edge57" class="edge">
<title>34&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4259.41,-1578.96C4267.77,-1591.11 4275.99,-1603.26 4284.05,-1615.4 4386.76,-1770.08 4476.97,-1964.2 4532.35,-2093.78"/>
<polygon fill="black" stroke="black" points="4529.14,-2095.17 4536.28,-2103 4535.58,-2092.43 4529.14,-2095.17"/>
</g>
<!-- 34&#45;&gt;17 -->
<g id="edge44" class="edge">
<title>34&#45;&gt;17</title>
<path fill="none" stroke="black" d="M4360.65,-1575.3C4363.78,-1576.69 4366.91,-1578.05 4370.05,-1579.4 4480.59,-1626.85 5352.61,-1547.72 5452.05,-1615.4 5588.36,-1708.17 5656.06,-1885.85 5689.42,-2027.43"/>
<polygon fill="none" stroke="black" points="5679.24,-2030.05 5696.02,-2057.04 5699.74,-2025.48 5679.24,-2030.05"/>
</g>
<!-- 34&#45;&gt;19 -->
<g id="edge45" class="edge">
<title>34&#45;&gt;19</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4360.38,-1575.01C4363.6,-1576.49 4366.82,-1577.95 4370.05,-1579.4 4483.66,-1630.27 4827.48,-1543.43 4929.05,-1615.4 5033.12,-1689.15 5096.43,-1812.51 5134.81,-1929.91"/>
<polygon fill="black" stroke="black" points="5131.48,-1930.99 5137.88,-1939.44 5138.15,-1928.85 5131.48,-1930.99"/>
</g>
<!-- 34&#45;&gt;21 -->
<g id="edge43" class="edge">
<title>34&#45;&gt;21</title>
<path fill="none" stroke="black" d="M4360.63,-1575.35C4363.76,-1576.72 4366.9,-1578.07 4370.05,-1579.4 4452.92,-1614.43 5919.85,-1557.91 5989.05,-1615.4 6152.01,-1750.8 6143.43,-2033.39 6129.12,-2165.36"/>
<polygon fill="none" stroke="black" points="6118.66,-2164.43 6125.46,-2195.48 6139.51,-2166.97 6118.66,-2164.43"/>
</g>
<!-- 34&#45;&gt;23 -->
<g id="edge46" class="edge">
<title>34&#45;&gt;23</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2777.46,-1046.71C2512.9,-1174.03 2246.55,-1357.48 2093.05,-1615.4 2057.27,-1675.52 2067.6,-2675.42 2072.31,-3023.32"/>
<polygon fill="black" stroke="black" points="2068.81,-3023.46 2072.44,-3033.42 2075.81,-3023.37 2068.81,-3023.46"/>
</g>
<!-- 34&#45;&gt;26 -->
<g id="edge41" class="edge">
<title>34&#45;&gt;26</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2988.54,-1579.01C2979.64,-1591.22 2970.81,-1603.36 2962.05,-1615.4 2957.23,-1622.03 2952.39,-1628.69 2947.52,-1635.39"/>
<polygon fill="none" stroke="black" points="2938.92,-1629.36 2929.8,-1659.81 2955.92,-1641.7 2938.92,-1629.36"/>
</g>
<!-- 34&#45;&gt;27 -->
<g id="edge48" class="edge">
<title>34&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4360.39,-1574.99C4363.6,-1576.48 4366.82,-1577.95 4370.05,-1579.4 4423.47,-1603.45 4856.07,-1572.59 4896.05,-1615.4 4943.2,-1665.89 4918.78,-2793.57 4896.05,-2858.8 4855.45,-2975.3 4475.39,-3446.79 4146.96,-3841.28"/>
<polygon fill="black" stroke="black" points="4144.14,-3839.2 4140.43,-3849.12 4149.52,-3843.68 4144.14,-3839.2"/>
</g>
<!-- 34&#45;&gt;29 -->
<g id="edge42" class="edge">
<title>34&#45;&gt;29</title>
<path fill="none" stroke="black" d="M3044.59,-1578.91C3040.51,-1591.06 3036.66,-1603.22 3033.05,-1615.4 2993.79,-1747.86 2991.81,-2726.94 3033.05,-2858.8 3051.46,-2917.65 3086.9,-2975.02 3122.64,-3022.53"/>
<polygon fill="none" stroke="black" points="3114.55,-3029.22 3141.27,-3046.42 3131.11,-3016.31 3114.55,-3029.22"/>
</g>
<!-- 34&#45;&gt;30 -->
<g id="edge49" class="edge">
<title>34&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3582.36,-1579.07C3586.4,-2056.68 3582.92,-2618.67 3550.05,-2858.8 3516.88,-3101.12 3490.22,-3160.2 3407.05,-3390.2 3403.86,-3399.03 3400.46,-3407.99 3396.91,-3416.99"/>
<polygon fill="black" stroke="black" points="3393.62,-3415.8 3393.16,-3426.39 3400.12,-3418.4 3393.62,-3415.8"/>
</g>
<!-- 34&#45;&gt;32 -->
<g id="edge54" class="edge">
<title>34&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3011.15,-1579.02C3006.88,-1591.12 3002.84,-1603.25 2999.05,-1615.4 2914.77,-1885.58 3003.43,-3978.87 3020.37,-4359.74"/>
<polygon fill="black" stroke="black" points="3016.88,-4360.16 3020.82,-4369.99 3023.87,-4359.84 3016.88,-4360.16"/>
</g>
<!-- 34&#45;&gt;33 -->
<g id="edge52" class="edge">
<title>34&#45;&gt;33</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3488.34,-1578.96C3464.54,-1811.32 3441.69,-2034.57 3429.71,-2151.56"/>
<polygon fill="black" stroke="black" points="3426.22,-2151.21 3428.69,-2161.52 3433.19,-2151.92 3426.22,-2151.21"/>
</g>
</g>
</svg>
