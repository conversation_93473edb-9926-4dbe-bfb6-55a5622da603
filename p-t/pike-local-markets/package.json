{"name": "pike-markets", "version": "0.1.0", "private": true, "description": "Pike Lending Markets", "homepage": "https://nuts.finance", "repository": "https://github.com/nutsfinance/pike-local-markets.git", "license": "MIT", "author": {"name": "NUTS Finance", "email": "<EMAIL>", "url": "https://nuts.finance"}, "main": "index.js", "scripts": {"build": "forge build", "build:optimized": "FOUNDRY_PROFILE=optimized forge build", "deploy:mainnet": "npx cannon build script/cannon/base-mainnet/base-mainnet.toml --rpc-url https://base-mainnet.public.blastapi.io", "deploy:mainnet:dryrun": "npx cannon build script/cannon/base-mainnet/base-mainnet.toml --rpc-url https://base-mainnet.public.blastapi.io --dry-run", "deploy:testnet": "npx cannon build script/cannon/base-sepolia/base-sepolia.toml --rpc-url https://sepolia.base.org", "deploy:testnet:dryrun": "npx cannon build script/cannon/base-sepolia/base-sepolia.toml --rpc-url https://sepolia.base.org --dry-run", "postinstall": "forge install", "lint:check": "yarn lint:sol-logic && forge fmt src --check", "lint:fix": "yarn sort-package-json && forge fmt && yarn solhint 'src/**/*.sol' 'test/**/*.sol' --fix", "lint:sol-logic": "solhint -c .solhint.json 'src/**/*.sol'", "lint:sol-tests": "solhint -c .solhint.json 'test/**/*.sol'", "prepare": "husky", "sort-package-json": "sort-package-json", "test": "forge test --match-contract Local -vv", "test:coverage": "forge coverage --match-contract Local -vv --no-match-coverage \"(script|test|node_modules)\"", "test:coverage:output": "forge coverage --match-contract Local -vv --no-match-coverage \"(script|test|node_modules)\" --report lcov && genhtml lcov.info --branch-coverage --output-dir coverage --ignore-errors inconsistent,category", "test:fork": "forge build --ast && npx cannon test script/cannon/base-mainnet/base-mainnet.toml --rpc-url https://base-mainnet.public.blastapi.io --forge.match-contract Fork --forge.vv", "test:fuzz": "forge test --match-contract Fuzz -vv"}, "dependencies": {"@chainlink/contracts": "^1.2.0", "@openzeppelin/contracts": "^5.0.2", "@openzeppelin/contracts-upgradeable": "^5.0.2", "@pythnetwork/pyth-sdk-solidity": "^4.0.0"}, "devDependencies": {"@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "17.0.3", "cannon-std": "git+https://github.com/usecannon/cannon-std.git#master", "forge-std": "git+https://github.com/foundry-rs/forge-std.git", "husky": "9.1.6", "solhint": "5.1.0", "solhint-plugin-prettier": "0.1.0", "sort-package-json": "2.10.0"}}